{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"ems-packages": {"projectType": "library", "root": "projects/ems-packages", "sourceRoot": "projects/ems-packages/src", "prefix": "lib", "architect": {"build": {"builder": "@angular-devkit/build-angular:ng-packagr", "options": {"project": "projects/ems-packages/ng-package.json"}, "configurations": {"production": {"tsConfig": "projects/ems-packages/tsconfig.lib.prod.json"}, "development": {"tsConfig": "projects/ems-packages/tsconfig.lib.json"}}, "defaultConfiguration": "production"}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "projects/ems-packages/src/test.ts", "tsConfig": "projects/ems-packages/tsconfig.spec.json", "karmaConfig": "projects/ems-packages/karma.conf.js"}}}}, "azaadi-packages": {"projectType": "library", "root": "projects/azaadi-packages", "sourceRoot": "projects/azaadi-packages/src", "prefix": "lib", "architect": {"build": {"builder": "@angular-devkit/build-angular:ng-packagr", "options": {"project": "projects/azaadi-packages/ng-package.json"}, "configurations": {"production": {"tsConfig": "projects/azaadi-packages/tsconfig.lib.prod.json"}, "development": {"tsConfig": "projects/azaadi-packages/tsconfig.lib.json"}}, "defaultConfiguration": "production"}}}, "logistic-packages": {"projectType": "library", "root": "projects/logistic-packages", "sourceRoot": "projects/logistic-packages/src", "prefix": "lib", "architect": {"build": {"builder": "@angular-devkit/build-angular:ng-packagr", "options": {"project": "projects/logistic-packages/ng-package.json"}, "configurations": {"production": {"tsConfig": "projects/logistic-packages/tsconfig.lib.prod.json"}, "development": {"tsConfig": "projects/logistic-packages/tsconfig.lib.json"}}, "defaultConfiguration": "production"}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "projects/logistic-packages/src/test.ts", "tsConfig": "projects/logistic-packages/tsconfig.spec.json", "karmaConfig": "projects/logistic-packages/karma.conf.js"}}}}, "spincabs-packages": {"projectType": "library", "root": "projects/spincabs-packages", "sourceRoot": "projects/spincabs-packages/src", "prefix": "lib", "architect": {"build": {"builder": "@angular-devkit/build-angular:ng-packagr", "options": {"project": "projects/spincabs-packages/ng-package.json"}, "configurations": {"production": {"tsConfig": "projects/spincabs-packages/tsconfig.lib.prod.json"}, "development": {"tsConfig": "projects/spincabs-packages/tsconfig.lib.json"}}, "defaultConfiguration": "production"}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "projects/spincabs-packages/src/test.ts", "tsConfig": "projects/spincabs-packages/tsconfig.spec.json", "karmaConfig": "projects/spincabs-packages/karma.conf.js"}}}}}, "cli": {"analytics": "05b04a6f-0435-48ab-b436-e73c5cdb9251"}}