import { Document, SchemaTimestampsConfig } from "mongoose";
import { ITrip } from "./ITrip.interface";
import { DriverWithdrawalRequestStatus, FuelType, TransactionType, ValidationStatus } from "../enums/IDriver.enum";
import { IDocument, IEmployee } from "./IAdmin.interface";
import { TripType } from "../enums/ITrip.enum";
import { ICity } from "./ISettings.interface";

interface IBase extends Document, SchemaTimestampsConfig {
}

export interface IDriver extends IBase {
    tAgent?: IEmployee | string,
    sDriverId?: string,
    sName: string,
    sEmail?: string,
    sGender?: string,
    sBloodGroup?: string,
    dDob?: Date,
    aPhoneNo: number,
    tAadhaar: IDocument | string;
    // aAadhaarNo: number;
    // sFrontAadhaarUrl?: string;
    // sBackAadhaarUrl?: string;
    tDL: IDocument | string;
    // sDLNo?: string;
    // sFrontDLUrl?: string;
    // sBackDLUrl?: string;
    tPan: IDocument | string;
    // sPanNo: string;
    // sFrontPanUrl?: string;
    // sBackPanUrl?: string;
    tProfilePic: IDocument | string;
    //sProfileUrl?: string;
    sAddress?: string,
    sDeviceToken?: string,
    bIsActive?: boolean,
    bIsOnline?: boolean,
    bCanLogin?: boolean,
    bIsDeleted?: boolean,
    aRatings?: number,
    bIsOutstationAllowed?: boolean,
    tRideCity?: ICity | string
    bIsVerified?: boolean
}
export interface ICarType extends IBase {
    sName: string,
    aCapacity?: number,
    sLogo?: string,
    aMinRecharge?: number,
    aWheel: number,
}
export interface ICarPricing extends IBase {
    tCarType: ICarType | string,
    sTripType: TripType | string,
    aBookingCharge?: number,
    aPhaseOneDistance?: number, // in Meter
    aPhaseOneFare?: number,     // Total Fare
    aPhaseTwoDistance?: number, // in meter
    aPhaseTwoFare?: number,     // per 1000 meter
    aPhaseThreeFare?: number,   // per 1000 meter
    aMinuteFare?: number,
    aDriverCharge?: number,
    aDrAccommodationCharge?: number
}
export interface ICar extends IBase {
    tCarType: ICarType | string,
    tAgent?: IEmployee | string,
    tDriver?: IDriver | string,
    tCarPricing?: ICarPricing | string,
    sColor?: string,
    tRc: IDocument | string;
    // sRegNo: string;
    // sFrontRCUrl?: string,
    // sBackRCUrl?: string,
    // dRegExpDate?: Date;
    tInsurance: IDocument | string;
    // sInsuranceNo?: string;
    // sFrontInsuranceUrl?: string;
    // dInsExpDate?: Date;
    tPUC: IDocument | string;
    // sPUCCertNo?: string;
    // sPUCUrl?: string;
    // dPucExpDate?: Date;
    tPermit: IDocument | string;
    // sPermitNo?: string;
    // sFrontPermitUrl?: string;
    // dPermitExpDate: Date;
    tFitness: IDocument | string;
    // sFitnessNo?: string;
    // sFrontFitnessUrl?: string;
    // dFitnessExpDate?: string;
    tVehiclePic: IDocument | string;
    bIsDriverIsOwner?: boolean,
    sLatitude?: string,
    sLongitude?: string,
    aBearingAngel?: number,
    isPrivate?: boolean,
    tModel: ICarModel | string;
    bIsVerified?: boolean
}
export interface IBankDetails extends IBase {
    tDriver: IDriver | string,
    sEmail?: string,
    aPhoneNo?: number,
    sAccountHolderName: string,
    sAccountType?: string,
    sAccountNo: string,
    sIFSCCode: string,
    sBankName: string,
    sBranchName: string
}
export interface IDriverWallet extends IBase {
    tDriver: IDriver | string,
    aAmount?: number,
}
export interface IDriverWalletTransaction extends IBase {
    dTransactionDate: Date,
    tDriver: IDriver | string,
    tDriverWallet?: IDriverWallet | string,
    tTrip?: ITrip | string,
    eType: TransactionType | string,  // CR|DR
    sTransactionId?: string,
    aAmount: number,
    sMessage?: string,
    sPaymentInstrumentType?: string,
    sPaymentState?: string,
    sMerchantTransactionId?: string
}
export interface IRentalCarPricing extends IBase {
    tCarType: ICarType | string,
    sPackage: string,
    aDistance: number,
    aDuration: number,
    aBookingCharge: number,
    aKmCharge: number,
    aMinCharge: number,
    aExtKmCharge: number,
    aExtMinCharge: number
}
export interface IDriverWithdrawalRequest extends IBase {
    tDriver: IDriver | string,
    tDriverWallet: IDriverWallet | string,
    aAmount: number,
    eStatus: DriverWithdrawalRequestStatus,
    dTransactionDate: Date,
    tBankAccountDetails: IBankDetails | string
}
export interface ICarModel extends IBase {
    sName: string;//unique:sTitle (eFuelType)
    sTitle: string;
    eFuelType: FuelType;
    eStatus: ValidationStatus;
    sRemarks?: string
}