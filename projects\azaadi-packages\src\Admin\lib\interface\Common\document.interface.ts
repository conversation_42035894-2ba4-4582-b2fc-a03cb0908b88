import { Types } from 'mongoose';
import { IUserInfo } from '../User';
import { IConcept } from './common.interface';
export interface IDocument {
  /** Name of the document */
  sName: string;

  /** URL or path to the document */
  sUrl: string;

  /** Type of the document */
  eType: DocumentType;

  /** Size of the document in bytes */
  aSize: number;

  /** MIME type of the document */
  sMimeType: string;

  /** Upload date of the document */
  dUploadDate: Date;

  /** Reference to the uploader */
  tUploader: string | Types.ObjectId | IUserInfo;
}
export interface IDocumentModel extends IConcept, IDocument {}
