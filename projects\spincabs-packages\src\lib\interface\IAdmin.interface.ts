import { Document, SchemaTimestampsConfig } from "mongoose";
import { DocumentsType, ValidationStatus } from "../enums/IDriver.enum";
interface IBase extends Document, SchemaTimestampsConfig {
}
export interface IEmployee extends IBase {
    sEmployeeId?: string,
    sName: string,
    sEmail: string,
    sPassword: string,
    aPhoneNo: number,
    dDob?: Date,
    sGender?: string,
    sAddress?: string,
    tRole: IRole | string,
    sProfileUrl?: string,
    sDeviceToken?: string,
    bIsActive?: boolean,
    bCanLogin?: boolean,
    bIsDeleted?: boolean
}
export interface IRole extends IBase {
    sName: string,
    sTag: string
}
export interface IManageAccess extends IBase {
    tRole: IRole | string,
    sModuleName: string,
    sModuleCode: string,
    bIsView: boolean,
    bIsWrite: boolean
}

export interface ICorporate extends IBase {
    sName: string,
    sUniqueTag: string,
    aDiscountPercent: number,
    bIsDeleted: boolean,
}

export interface IDocument extends IBase {
    sReferenceId: string,
    sDocumentName: DocumentsType,
    sDocumentNo?: string,
    dRegistrationDate?: Date,
    dExpDate?: Date,
    sFrontImageUrl: string,
    sBackImageUrl?: string,
    eStatus: ValidationStatus,
    sRemarks?: string
}