.wrapper {
  flex: 1;
  max-width: 450px;
}
.wrapper .login-form h5 {
  display: flex;
  justify-content: space-between;
  margin-top: 10px;
}
.wrapper .login-form .btn-submit {
  display: block;
  cursor: pointer;
  outline: none;
  width: 100%;
  border: 1px solid #00e5e5;
  border-radius: 5px;
  margin-top: 50px;
  font-size: 1.5em;
  line-height: 2;
  color: #fff;
  background: #00e5e5;
  transition: 0.3s;
}
.wrapper .login-form .btn-submit:hover {
  box-shadow: 0 6px 12px -3px rgba(9, 30, 66, 0.4);
  transform: translate(0, -3px);
  background: #00e5e5;
}

.input-field {
  position: relative;
  margin: 25px 0;
  padding-left: 30px;
  color: #00e5e5;
}
.input-field input {
  background-color: transparent;
  border: none;
  outline: none;
  border-radius: 0;
  width: 100%;
  font-size: 1.4em;
  height: 40px;
  border-bottom: 1px solid #00e5e5;
  color: inherit;
  transition: 0.2s ease;
}
.input-field input::placeholder {
  color: transparent;
}
.input-field input:not(:-moz-placeholder-shown) {
  border-bottom: 1px solid #00e5e5;
  box-shadow: 0 1px 0 0 #00e5e5;
}
.input-field input:not(:-ms-input-placeholder) {
  border-bottom: 1px solid #00e5e5;
  box-shadow: 0 1px 0 0 #00e5e5;
}
.input-field input:focus,
.input-field input:not(:placeholder-shown) {
  border-bottom: 1px solid #00e5e5;
  box-shadow: 0 1px 0 0 #00e5e5;
}
.input-field input:not(:-moz-placeholder-shown) + label {
  transform: translateY(-14px) scale(0.85);
  color: #00e5e5;
}
.input-field input:not(:-ms-input-placeholder) + label {
  transform: translateY(-14px) scale(0.85);
  color: #00e5e5;
}
.input-field input:focus + label,
.input-field input:not(:placeholder-shown) + label {
  transform: translateY(-14px) scale(0.85);
  color: #00e5e5;
}
.input-field label {
  color: #00e5e5;
  position: absolute;
  top: 0;
  left: 30px;
  font-size: 1em;
  cursor: text;
  transform-origin: 0% 100%;
  transform: translateY(14px);
  transition: 0.2s ease-out;
}
.input-field i {
  position: absolute;
  top: 0;
  left: 0;
  font-size: 1.2em;
  width: 30px;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #00e5e5;
}

/// group

._content {
  background-color: rgba(237, 237, 237, 0.96);
  ._heading {
    background-color: rgba(255, 255, 255, 0.96);
    // box-shadow: 0 0 25px rgba(0,0,0,0.2);
    // border-radius: 1rem;
    // border: 1px solid #00e5e5;
    padding: 2vh;
    margin-bottom: 5px;
    ._title {
      color: #00e5e5;
      font-size: 20px;
      text-align: center;
    }
    ._body {
      color: #00e5e5;
      font-size: 14px;
      text-align: center;
    }
  }

  ._group {
    background-color: rgba(255, 255, 255, 0.96);
    box-shadow: 0 0 25px rgba(0, 0, 0, 0.2);
    border-radius: 1rem;
    border: 1px solid #00e5e5;
    padding: 2vh;
    margin-bottom: 5px;
  }

  ._row {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    width: 100%;
  }

  ._col {
    display: flex;
    flex-direction: column;
    flex-basis: 100%;
    margin: 3px;
    text-align: center;
    border-radius: 4px;
    padding: 10px;
    width: fit-content;
  }

  ._head {
    padding-left: 1.2em;
    color: #00e5e5;
    text-align: center;
    display: flex;
    flex-direction: row;
  }

  ._head:before,
  ._head:after {
    content: "";
    flex: 1 1;
    border-bottom: 2px solid #00e5e5;
    margin: auto;
  }

  ._head h1 {
    padding-left: 10px;
    padding-right: 10px;
  }
  @media screen and (min-width: 800px) {
    ._col {
      flex: 1;
    }
  }
}

// Select
.input-field select {
  background-color: #fff;
  color: #00e5e5;
  font-size: 14px;
  padding: 10px;
  border-radius: 5px;
  border: 1px solid #00e5e5;
  width: 100%;
  cursor: pointer;
}

.input-field option:checked {
  background-color: #00e5e5;
  color: #ffffff;
}

.input-field option:hover {
  background-color: #00e5e5;
  color: #ffffff;
}

/* styles for the dropdown arrow */
.input-field select::before {
  content: "";
  width: 0;
  height: 0;
  border-left: 52px solid transparent;
  border-right: 2px solid transparent;
  border-top: 2px solid #00e5e5;
  position: absolute;
  right: 10px;
  top: 15px;
  pointer-events: none;
}
.input-field select:active,
.input-field select:focus {
  outline: none;
  box-shadow: none;
}
// input radio button & checkbox
.control-group {
   display: inline-block;
   text-align: left;
}

.control {
  display: block;
  position: relative;
  padding-left: 30px;
  margin-bottom: 15px;
  cursor: pointer;
  font-size: 18px;
  color: #00e5e5;
  text-align: left;
}

.control-group i {
  position: absolute;
  top: 0;
  left: 0;
  font-size: 1.2em;
  width: 30px;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #00e5e5;
}

.control input {
  position: absolute;
  z-index: -1;
  opacity: 0;
}
.control__indicator {
  position: absolute;
  top: 2px;
  left: 0;
  height: 20px;
  width: 20px;
  background: #e6e6e6;
}
.control--radio .control__indicator {
  border-radius: 50%;
}
.control:hover input ~ .control__indicator,
.control input:focus ~ .control__indicator {
  background: #ccc;
}
.control input:checked ~ .control__indicator {
  background: #00e5e5;
}
.control:hover input:not([disabled]):checked ~ .control__indicator,
.control input:checked:focus ~ .control__indicator {
  background: #00e5e5;
}
.control input:disabled ~ .control__indicator {
  background: #e6e6e6;
  opacity: 0.6;
  pointer-events: none;
}
.control__indicator:after {
  content: '';
  position: absolute;
  display: none;
}
.control input:checked ~ .control__indicator:after {
  display: block;
}
.control--checkbox .control__indicator:after {
  left: 8px;
  top: 4px;
  width: 3px;
  height: 8px;
  border: solid #fff;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}
.control--checkbox input:disabled ~ .control__indicator:after {
  border-color: #7b7b7b;
}
.control--radio .control__indicator:after {
  left: 7px;
  top: 7px;
  height: 6px;
  width: 6px;
  border-radius: 50%;
  background: #fff;
}
.control--radio input:disabled ~ .control__indicator:after {
  background: #7b7b7b;
}
