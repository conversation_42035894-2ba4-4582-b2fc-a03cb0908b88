import { EmsDeliveryType } from "../enums/chat.enum";
export interface EmsChatUser {
    _id?: string;
    tEmsUserId?: string;
    sName: string;
    bIsAdmin: boolean;
    sProfileUrl: string;
    createdAt?: Date;
    updatedAt?: Date;
}
export interface EmsChatGroup {
    _id?: string;
    tIdChatUsers: EmsChatUser[] | string[];
    aCount?: number;
    createdAt?: Date;
    updatedAt?: Date;
}
export interface EmsConversion {
    _id?: string;
    sTitle: string;
    tIdGroup?: string | EmsChatGroup;
    bIsSolved: boolean;
    createdAt?: Date;
    updatedAt?: Date;
}
export interface EmsChat {
    _id?: string;
    sMessage: string;
    tSenders: EmsChatUser | string;
    tSeen: EmsSeenBy[] | string[];
    tIdConversations: EmsConversion | string
    createdAt?: Date;
    updatedAt?: Date;
}
export interface EmsSeenBy {
    _id?: string;
    tChatUserId: string | EmsChatUser;
    eDeliveryType: EmsDeliveryType;
    createdAt?: Date;
    updatedAt?: Date;
}