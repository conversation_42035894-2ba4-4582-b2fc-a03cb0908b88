import { IUserInfo } from '../User';

// Reuse from IUserInfo, omitting the fields that differ in DTO
export interface IUserInfoDTO
  extends Omit<
    IUserInfo,
    'tRoleId' | 'tOrganizationId' | 'tDepartmentId' | 'tEmpCode' | 'sPassword'
  > {
  /** Role of the user (as string ID) */
  tRoleId?: string;

  /** Organization ID (as string) */
  tOrganizationId?: string;

  /** Department ID (as string) */
  tDepartmentId?: string;

  /** Password of the user (optional in DTO) */
  sPassword?: string;

  /** Employee Code (renamed in DTO) */
  empCode: string;
}
