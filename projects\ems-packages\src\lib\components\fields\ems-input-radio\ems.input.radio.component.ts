import { Component, Input, OnInit } from '@angular/core';
import { EmsInputFieldComponent } from '../ems-input-field.component';

@Component({
  selector: 'ems-input-radio',
  template: `
    <ng-container>
      <div class="">
        <i class="fas fa-user"></i>
        <label class="control control--radio"
          >First radio
          <input type="radio" name="radio" checked="checked" />
          <div class="control__indicator"></div>
        </label>
        <label class="control control--radio"
          >Second radio
          <input type="radio" name="radio" />
          <div class="control__indicator"></div>
        </label>
      </div>
    </ng-container>
  `,
  styles: [],
})
export class EmsInputRadioComponent
  extends EmsInputFieldComponent
  implements OnInit
{
  @Input() sValue: string | undefined;
  @Input() sDefaultValue: string | undefined;
  constructor() {
    super();
  }
  ngOnInit(): void {
    this.addControlToForm(this.sDefaultValue);
  }

  onTextValueChange(event: Event) {
    var ddd = (event.target as HTMLInputElement).value;
    this._control.setValue(ddd);
    this._control.markAsDirty();
    this._control.updateValueAndValidity();
    this.emsChange.emit(this._control);
  }
}
