import { Document, SchemaTimestampsConfig } from "mongoose";
import { DiscountType } from "../enums/IDiscount.enum";
import { ICustomer } from "./ICustomer.interface";

interface IBase extends Document, SchemaTimestampsConfig {
}

export interface ICouponCode extends IBase {
    sCouponCode: string,
    aExpiresIn: number,
    aMinAmount: number,
    eDisCountType: DiscountType | string,
    aDisCountValue: number,
    bIsActive?: boolean,
    bIsForAll?: boolean,
    aMaxUse?: number
}
export interface ICustomerCoupon extends IBase {
    tCoupon: ICouponCode | string,
    tCustomer: ICustomer | string,
    sCode?: string,
    dStartDate: Date,
    dEndDate?: Date,
    bIsActive?: boolean,
    aUsageCount?: number,
}