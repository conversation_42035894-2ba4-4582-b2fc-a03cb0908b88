import { EmsDynamicForm } from './../../projects/ems-packages/src/lib/interface/dynamicForm.interface';
import { Component } from '@angular/core';
import { FormControl, FormGroup } from '@angular/forms';

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.scss'],
})
export class AppComponent {
  dynamicForm: EmsDynamicForm = {
    // sTitle: 'Okk',
    // sContent: 'Hello',
    oInputGroups: [{
      sTitle: 'Rizwan',
      oGroupRows: [{
        oInputFields: [
          {
            sLabel: 'Password',
            sInputType: 'password',
            sPlaceholder: 'sPlaceholder'
          }, {
            sLabel: 'Title',
            sInputType: 'select',
            sPlaceholder: 'sPlaceholder'
          }, {
            sLabel: 'Title',
            sInputType: 'checkbox',
            sPlaceholder: 'sPlaceholder'
          }, {
            sLabel: 'Title',
            sInputType: 'radio',
            sPlaceholder: 'sPlaceholder'
          }, {
            sLabel: 'Email',
            sInputType: 'email',
            sPlaceholder: 'sPlaceholder'
          }, {
            sLabel: 'Title',
            sInputType: 'tel',
            sPlaceholder: 'sPlaceholder'
          }, {
            sLabel: 'Title',
            sInputType: 'date',
            sPlaceholder: 'sPlaceholder'
          }
        ],
      },{
        oInputFields: [
           {
            sLabel: 'Title',
            sInputType: 'select',
            sPlaceholder: 'sPlaceholder'
          }
        ],
      }],

    }, {
      sTitle: 'Title',
      oGroupRows: [{
        oInputFields: [
          {
            sLabel: 'Title',
            sInputType: 'checkbox',
            sPlaceholder: 'sPlaceholder'
          }, {
            sLabel: 'Title',
            sInputType: 'radio',
            sPlaceholder: 'sPlaceholder'
          }
        ],
      }],

    }]

  }
  constructor() {
  }
}
