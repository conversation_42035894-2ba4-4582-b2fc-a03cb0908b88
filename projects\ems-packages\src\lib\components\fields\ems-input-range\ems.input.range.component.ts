import { Component, Input, OnInit } from '@angular/core';
import { EmsInputFieldComponent } from '../ems-input-field.component';

@Component({
  selector: 'ems-input-range',
  template: `
    <ng-container>
      <div class="input-field">
        <i class="fas fa-user"></i>
        <select>
          <option value="option1">Option 1</option>
          <option value="option2">Option 2</option>
          <option value="option3">Option 3</option>
        </select>
        <label for="{{ sTags }}">{{ sLabel }}</label>
      </div>
    </ng-container>
  `,
  styles: [],
})
export class EmsInputRangeComponent
  extends EmsInputFieldComponent
  implements OnInit
{
  @Input() sValue: string | undefined;
  @Input() sDefaultValue: string | undefined;
  constructor() {
    super();
  }
  ngOnInit(): void {
    this.addControlToForm(this.sDefaultValue);
  }

  onTextValueChange(event: Event) {
    var ddd = (event.target as HTMLInputElement).value;
    this._control.setValue(ddd);
    this._control.markAsDirty();
    this._control.updateValueAndValidity();
    this.emsChange.emit(this._control);
  }
}
