export interface IAction {
  icon?: string;
  label: string;
  action: () => void;
}

export interface ILanguage {
  name: string;
  code: string;
}

export interface IColumn {
  field: string;
  header: string;
}

export interface IdentityInfo extends ITagType {
  title: string;
  icon?: string;
  tag?: string;
  noOfCols: number;
  values: IdentityInfoValue[];
}
export interface IdentityInfoValue {
  label: string;
  value: string | number;
  valueHexColor?: string;
}

export interface InfoCardInput {
  title: string;
  values: InfoCardValue[];
}

interface InfoCardValue {
  label: string;
  value: string | number;
  valueHexColor?: string;
}
export interface ITagType {
  tagType?:
    | 'success'
    | 'info'
    | 'warning'
    | 'danger'
    | 'primary'
    | 'secondary'
    | 'light'
    | 'dark';
}
export interface IQuickLink {
  label: string;
  link: string;
}
