import { Component, Input, OnInit } from '@angular/core';
import { EmsInputFieldComponent } from '../ems-input-field.component';

@Component({
  selector: 'ems-input-date',
  template: `
    <ng-container>
      <div class="input-field">
        <i class="fas fa-user"></i>
        <input type="datetime-local" >
        <label for="{{ sTags }}">{{ sLabel }}</label>
      </div>
    </ng-container>
  `,
  styles: [],
})
export class EmsInputDateComponent
  extends EmsInputFieldComponent
  implements OnInit
{
  @Input() sValue: string | undefined;
  @Input() sDefaultValue: string | undefined;
  constructor() {
    super();
  }
  ngOnInit(): void {
    this.addControlToForm(this.sDefaultValue);
  }

  onTextValueChange(event: Event) {
    var ddd = (event.target as HTMLInputElement).value;
    this._control.setValue(ddd);
    this._control.markAsDirty();
    this._control.updateValueAndValidity();
    this.emsChange.emit(this._control);
  }
}
