import { EmsCategory } from './category.interface';
import { EmsOption } from './option.interface';
import { EmsQuestionType } from './questionType.interface';
import { EmsUser } from '../user.interface';

export interface EmsQuestion {
    _id?: string;
    sName: string;
    tType: string | EmsQuestionType;
    tCategory: string | EmsCategory;
    bMandatory?: boolean;
    aPoint?: number;
    aDuration?: number;
    tUser?: string | EmsUser;
    createdAt?: Date;
    updatedAt?: Date;
    sTag?: string;
    aNegativePoint?: number;
    bIsRandomize?: boolean;
    sImageUrl?: string;
    sVideoUrl?: string;
    tUserAns?: string | EmsOption[];
    aUserPoint?: number;
    sFeedback?: string;
    tOptions?: EmsOption[];
}
