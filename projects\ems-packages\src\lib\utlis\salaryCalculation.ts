import * as moment from 'moment';
import { EmsGovtSub, EmsSkillCategory } from '../enums/managements.enum';
import {
  EmsNonPfSalary,
  EmsPfSalary,
  EmsUpdatedNonPfSalary,
  EmsUpdatedPfEsiSalary,
  EmsUpdatedPfSalary,
  InputForPFSlip,
  P<PERSON>lip,
} from '../interface/salary.interface';

export const calculateNonPfSalary = (
  aMonthDays: number,
  aPaidDays: number,
  aMonthlyGross: number,
  aConveyance: number,
  aMedicalInsurance: number,
  aAdvanceOpening?: number,
  aFurtherAdvance?: number,
  aAdvanceAdjusted?: number
) => {
  const nonPfSalary: EmsNonPfSalary = {} as EmsNonPfSalary;
  nonPfSalary.aMonthDays = aMonthDays;
  nonPfSalary.aPaidDays = aPaidDays;
  nonPfSalary.aMonthlyGross = parseFloat(aMonthlyGross.toFixed(2));
  nonPfSalary.aBasic = parseFloat(
    (
      ((aMonthlyGross / nonPfSalary.aMonthDays) * nonPfSalary.aPaidDays) /
      2
    ).toFixed(2)
  );
  nonPfSalary.aHRA = parseFloat((nonPfSalary.aBasic / 2 - 1600).toFixed(2));
  nonPfSalary.aConveyanceAllowance = parseFloat(aConveyance.toFixed(2));
  nonPfSalary.aBasketAllowance = parseFloat(
    (
      nonPfSalary.aBasic * 2 -
      nonPfSalary.aHRA -
      nonPfSalary.aConveyanceAllowance -
      nonPfSalary.aBasic
    ).toFixed(2)
  );
  nonPfSalary.aEarnedGross = parseFloat(
    (
      nonPfSalary.aBasic +
      nonPfSalary.aHRA +
      nonPfSalary.aConveyanceAllowance +
      nonPfSalary.aBasketAllowance
    ).toFixed(2)
  );
  nonPfSalary.aAdvanceOpening = aAdvanceOpening || 0;
  nonPfSalary.aFurtherAdvance = aFurtherAdvance || 0;
  nonPfSalary.aAdvanceAdjusted = aAdvanceAdjusted || 0;
  nonPfSalary.aClosing =
    nonPfSalary.aAdvanceOpening +
    nonPfSalary.aFurtherAdvance -
    nonPfSalary.aAdvanceAdjusted;
  nonPfSalary.aMedicalInsurance = parseFloat(aMedicalInsurance.toFixed(2));
  nonPfSalary.aTDS = parseFloat(
    ((nonPfSalary.aEarnedGross * 1) / 100).toFixed(2)
  );
  nonPfSalary.aPTax = 0;
  nonPfSalary.aNetAmount = parseFloat(
    (nonPfSalary.aEarnedGross - nonPfSalary.aTDS - nonPfSalary.aPTax).toFixed(2)
  );
  nonPfSalary.aCTC = parseFloat(
    (nonPfSalary.aEarnedGross + aMedicalInsurance).toFixed(2)
  );
  return nonPfSalary;
};

export const calculatePf1Salary = (
  aMonthDays: number,
  aPaidDays: number,
  eCategory: number,
  bEPFApplicability: boolean,
  bRestrictPF: boolean,
  aVoluntaryEPF: number,
  bESICApplicability: boolean,
  bPTaxApplicability: boolean,
  aMBaseRate: number,
  aMOldBasic: number,
  aErnIncentive: number,
  eGovtSubsidies: EmsGovtSub,
  aEedOtherDeduction: number,
  aEedTDS: number,
  aEedLWF: number,
  aAdvanceOpening: number,
  aFurtherAdvance: number,
  aAdvanceAdjusted: number,
  dDob: Date,
  aErcAdditional: number
) => {
  const salary: EmsPfSalary = {} as EmsPfSalary;
  //init
  salary.eCategory = eCategory;
  salary.aMinimumWages =
    eCategory == EmsSkillCategory.Unskilled
      ? 9800
      : eCategory == EmsSkillCategory.SemiSkilled
      ? 10800
      : eCategory == EmsSkillCategory.Skilled
      ? 11900
      : eCategory == EmsSkillCategory.HighlySkilled
      ? 13100
      : eCategory == EmsSkillCategory.NotFollowed
      ? 0
      : 0;
  salary.bEPFApplicability = bEPFApplicability;
  salary.bRestrictPF = bRestrictPF;
  salary.aVoluntaryEPF = aVoluntaryEPF;
  salary.bESICApplicability = bESICApplicability;
  salary.bPTaxApplicability = bPTaxApplicability;
  salary.aMBaseRate = aMBaseRate;
  salary.aMOldBasic = aMOldBasic;
  salary.dDob = dDob;
  salary.eGovtSub = eGovtSubsidies;

  salary.aMonthDays = aMonthDays;
  salary.aPaidDays = aPaidDays;
  //calculate
  // MASTER
  salary.aMBasicRate =
    aMBaseRate <= 0
      ? 0
      : aMOldBasic <= salary.aMinimumWages
      ? Math.round((aMBaseRate * 50) / 100) <= salary.aMinimumWages
        ? salary.aMinimumWages
        : Math.round((aMBaseRate * 50) / 100)
      : aMOldBasic > salary.aMinimumWages
      ? aMOldBasic
      : Math.round((aMBaseRate * 50) / 100) <=
        (aMOldBasic > salary.aMinimumWages ? aMOldBasic : salary.aMinimumWages)
      ? aMOldBasic > salary.aMinimumWages
        ? aMOldBasic
        : salary.aMinimumWages
      : Math.round((aMBaseRate * 50) / 100);
  salary.aMConveyance =
    salary.aMBaseRate - salary.aMBasicRate < 1600 ? 0 : 1600;
  salary.aMHraRate =
    Math.round((aMBaseRate * 30) / 100) <=
    aMBaseRate - salary.aMBasicRate - salary.aMConveyance
      ? Math.round((aMBaseRate * 30) / 100)
      : aMBaseRate - salary.aMBasicRate - salary.aMConveyance;
  salary.aMBasketAllowance =
    aMBaseRate - (salary.aMBasicRate + salary.aMHraRate + salary.aMConveyance);
  salary.aMGrossRate =
    salary.aMBasicRate +
    salary.aMHraRate +
    salary.aMConveyance +
    salary.aMBasketAllowance;
  // ERNNGS
  salary.aErnBasic = Math.round(
    (salary.aMBasicRate / salary.aMonthDays) * salary.aPaidDays
  );
  salary.aErnHra = Math.round(
    (salary.aMHraRate / salary.aMonthDays) * salary.aPaidDays
  );
  salary.aErnConveyance = Math.round(
    (salary.aMConveyance / salary.aMonthDays) * salary.aPaidDays
  );
  salary.aErnBasketAllowance = Math.round(
    (salary.aMBasketAllowance / salary.aMonthDays) * salary.aPaidDays
  );
  salary.aErnGrossEarning =
    salary.aErnBasic +
    salary.aErnHra +
    salary.aErnConveyance +
    salary.aErnBasketAllowance;
  salary.aErnIncentive = aErnIncentive;
  salary.aErnTotalEarning = salary.aErnGrossEarning + aErnIncentive;
  // EE DEDUCTION
  salary.aEedPf =
    bEPFApplicability == false
      ? 0
      : bRestrictPF == false
      ? Math.round((salary.aErnBasic * 12) / 100)
      : salary.aErnBasic > 15000
      ? Math.round((15000 * 12) / 100)
      : Math.round((salary.aErnBasic * 12) / 100);
  salary.aEedPfVoluntary =
    aVoluntaryEPF <= 0
      ? 0
      : Math.round((salary.aErnBasic * aVoluntaryEPF) / 100);
  salary.aEedESI =
    bESICApplicability == false
      ? 0
      : salary.aErnTotalEarning <= 176 * salary.aPaidDays
      ? 0
      : Math.ceil(
          ((salary.aErnTotalEarning - salary.aErnConveyance) * 0.75) / 100
        );
  salary.aEedPTax =
    bPTaxApplicability == false
      ? 0
      : salary.aErnTotalEarning <= 10000
      ? 0
      : salary.aErnTotalEarning <= 15000
      ? 110
      : salary.aErnTotalEarning <= 25000
      ? 130
      : salary.aErnTotalEarning <= 40000
      ? 150
      : 200;
  salary.aEedOtherDeduction = aEedOtherDeduction;
  salary.aEedTDS = aEedTDS;
  salary.aEedLWF = aEedLWF;
  salary.aNetPayable =
    salary.aErnTotalEarning -
    (salary.aEedPf +
      salary.aEedPfVoluntary +
      salary.aEedESI +
      salary.aEedPTax +
      salary.aEedOtherDeduction +
      salary.aEedTDS +
      salary.aEedLWF);
  salary.aAdvanceOpening = aAdvanceOpening;
  salary.aFurtherAdvance = aFurtherAdvance;
  salary.aAdvanceAdjusted = aAdvanceAdjusted;
  salary.aClosing =
    salary.aAdvanceOpening + salary.aFurtherAdvance - salary.aAdvanceAdjusted;
  salary.aEpfAbryReimbursement =
    eGovtSubsidies == EmsGovtSub.ABRY ? salary.aEedPf : 0;
  salary.aBankDisbursment =
    salary.aNetPayable - salary.aAdvanceAdjusted + salary.aEpfAbryReimbursement;
  if (salary.dDob) {
    salary.aErcEmployerEPS =
      bEPFApplicability == false
        ? 0
        : Math.abs(
            new Date().getFullYear() - new Date(salary.dDob).getFullYear()
          ) >= 58
        ? 0
        : salary.aErnBasic > 15000
        ? Math.round((15000 * 8.33) / 100)
        : Math.round((salary.aErnBasic * 8.33) / 100);
  } else salary.aErcEmployerEPS = 0;
  salary.aErcEmployerEPF = salary.aEedPf - salary.aErcEmployerEPS;
  salary.aErcAdminCharges =
    bEPFApplicability == false
      ? 0
      : bRestrictPF == false
      ? Math.round((salary.aErnBasic * 0.5) / 100)
      : salary.aErnBasic > 15000
      ? Math.round((15000 * 0.5) / 100)
      : Math.round((salary.aErnBasic * 0.5) / 100);
  salary.aErcEDLICharges =
    bEPFApplicability == false
      ? 0
      : salary.aErnBasic > 15000
      ? Math.round((15000 * 0.5) / 100)
      : Math.round((salary.aErnBasic * 0.5) / 100);
  salary.aErcEsiEmployer =
    bESICApplicability == false
      ? 0
      : parseFloat(((salary.aErnTotalEarning * 3.25) / 100).toFixed(2));
  salary.aErcAdditional = aErcAdditional;
  salary.aMonthlyCTC =
    salary.aErcEmployerEPS +
    salary.aErcEmployerEPF +
    salary.aErcAdminCharges +
    salary.aErcEDLICharges +
    salary.aErcEsiEmployer +
    salary.aErcAdditional +
    salary.aErnTotalEarning;
  return salary;
};
export const calculatePf2Salary = (
  aMonthDays: number,
  aPaidDays: number,
  eCategory: number,
  bEPFApplicability: boolean,
  bRestrictPF: boolean,
  aVoluntaryEPF: number,
  bESICApplicability: boolean,
  bPTaxApplicability: boolean,
  aMBaseRate: number,
  aMOldBasic: number,
  aErnIncentive: number,
  eGovtSubsidies: EmsGovtSub,
  aEedOtherDeduction: number,
  aEedTDS: number,
  aEedLWF: number,
  aAdvanceOpening: number,
  aFurtherAdvance: number,
  aAdvanceAdjusted: number,
  dDob: Date,
  aEedPfVoluntary: number,
  aErcMedicalInsurance: number
) => {
  const salary: EmsPfSalary = {} as EmsPfSalary;
  //init
  salary.eGovtSub = eGovtSubsidies;
  salary.eCategory = eCategory;
  salary.aMinimumWages =
    eCategory == EmsSkillCategory.Unskilled
      ? 9800
      : eCategory == EmsSkillCategory.SemiSkilled
      ? 10800
      : eCategory == EmsSkillCategory.Skilled
      ? 11900
      : eCategory == EmsSkillCategory.HighlySkilled
      ? 13100
      : eCategory == EmsSkillCategory.NotFollowed
      ? 0
      : 0;
  salary.aVoluntaryEPF = aVoluntaryEPF;
  salary.bEPFApplicability = bEPFApplicability;
  salary.bRestrictPF = bRestrictPF;
  salary.bESICApplicability = bESICApplicability;
  salary.bPTaxApplicability = bPTaxApplicability;
  salary.aMonthDays = aMonthDays;
  salary.aPaidDays = aPaidDays;
  salary.dDob = dDob;

  //calculate
  // MASTER
  salary.aMBaseRate = aMBaseRate;
  salary.aMOldBasic = aMOldBasic;
  salary.aMGrossRate = aMBaseRate;
  salary.aMBasicRate =
    bEPFApplicability == false
      ? salary.aMGrossRate < 15001
        ? salary.aMGrossRate
        : Math.round((salary.aMGrossRate * 50) / 100) < 15001
        ? Math.max(15100)
        : Math.round((salary.aMGrossRate * 50) / 100)
      : aMOldBasic >= salary.aMinimumWages
      ? aMOldBasic
      : salary.aMGrossRate <= salary.aMinimumWages
      ? salary.aMGrossRate
      : Math.round((salary.aMGrossRate * 50) / 100) <= salary.aMinimumWages
      ? salary.aMinimumWages
      : Math.round((salary.aMGrossRate * 50) / 100);
  salary.aMConveyance =
    salary.aMBaseRate - salary.aMBasicRate <= 1600 ? 0 : 1600;
  salary.aMHraRate =
    Math.round((aMBaseRate * 30) / 100) <=
    aMBaseRate - salary.aMBasicRate - salary.aMConveyance
      ? Math.round((aMBaseRate * 30) / 100)
      : aMBaseRate - salary.aMBasicRate - salary.aMConveyance;
  salary.aMBasketAllowance =
    salary.aMBaseRate -
    (salary.aMBasicRate + salary.aMHraRate + salary.aMConveyance);

  // ERNNGS
  salary.aErnBasic = Math.round(
    (salary.aMBasicRate / salary.aMonthDays) * salary.aPaidDays
  );
  salary.aErnHra = Math.round(
    (salary.aMHraRate / salary.aMonthDays) * salary.aPaidDays
  );
  salary.aErnConveyance = Math.round(
    (salary.aMConveyance / salary.aMonthDays) * salary.aPaidDays
  );
  salary.aErnBasketAllowance = Math.round(
    (salary.aMBasketAllowance / salary.aMonthDays) * salary.aPaidDays
  );
  salary.aErnGrossEarning =
    salary.aErnBasic +
    salary.aErnHra +
    salary.aErnConveyance +
    salary.aErnBasketAllowance;
  salary.aErnIncentive = aErnIncentive;
  salary.aErnTotalEarning = salary.aErnGrossEarning + aErnIncentive;

  // EE DEDUCTION
  salary.aEedPf =
    bEPFApplicability == false
      ? 0
      : bRestrictPF == false
      ? Math.round((salary.aErnBasic * 12) / 100)
      : salary.aErnBasic > 15000
      ? Math.round((15000 * 12) / 100)
      : Math.round((salary.aErnBasic * 12) / 100);
  salary.aEedPfVoluntary = aEedPfVoluntary;
  salary.aEedESI =
    bESICApplicability == false
      ? 0
      : salary.aErnTotalEarning <= 176 * salary.aPaidDays
      ? 0
      : Math.ceil(
          ((salary.aErnTotalEarning - salary.aErnConveyance) * 0.75) / 100
        );
  salary.aEedPTax =
    bPTaxApplicability == false
      ? 0
      : salary.aErnTotalEarning <= 10000
      ? 0
      : salary.aErnTotalEarning <= 15000
      ? 110
      : salary.aErnTotalEarning <= 25000
      ? 130
      : salary.aErnTotalEarning <= 40000
      ? 150
      : 200;
  salary.aEedOtherDeduction = aEedOtherDeduction;
  salary.aEedTDS = aEedTDS;
  salary.aEedLWF = aEedLWF;
  salary.aNetPayable =
    salary.aErnTotalEarning -
    (salary.aEedPf +
      salary.aEedPfVoluntary +
      salary.aEedESI +
      salary.aEedPTax +
      salary.aEedOtherDeduction +
      salary.aEedTDS +
      salary.aEedLWF);
  salary.aAdvanceOpening = aAdvanceOpening;
  salary.aFurtherAdvance = aFurtherAdvance;
  salary.aAdvanceAdjusted = aAdvanceAdjusted;
  salary.aClosing =
    salary.aAdvanceOpening + salary.aFurtherAdvance - salary.aAdvanceAdjusted;
  salary.aEpfAbryReimbursement =
    eGovtSubsidies == EmsGovtSub.ABRY ? salary.aEedPf : 0;
  salary.aBankDisbursment =
    salary.aNetPayable - salary.aAdvanceAdjusted + salary.aEpfAbryReimbursement;
  if (salary.dDob) {
    salary.aErcEmployerEPS =
      bEPFApplicability == false
        ? 0
        : Math.abs(
            new Date().getFullYear() - new Date(salary.dDob).getFullYear()
          ) >= 58
        ? 0
        : salary.aErnBasic > 15000
        ? Math.round((15000 * 8.33) / 100)
        : Math.round((salary.aErnBasic * 8.33) / 100);
  } else salary.aErcEmployerEPS = 0;

  //Er Contribution
  salary.aErcEmployerEPF = salary.aEedPf - salary.aErcEmployerEPS;
  salary.aErcAdminCharges =
    bEPFApplicability == false
      ? 0
      : bRestrictPF == false
      ? Math.round((salary.aErnBasic * 0.5) / 100)
      : salary.aErnBasic > 15000
      ? Math.round((15000 * 0.5) / 100)
      : Math.round((salary.aErnBasic * 0.5) / 100);
  salary.aErcEDLICharges =
    bEPFApplicability == false
      ? 0
      : salary.aErnBasic > 15000
      ? Math.round((15000 * 0.5) / 100)
      : Math.round((salary.aErnBasic * 0.5) / 100);
  salary.aErcEsiEmployer =
    bESICApplicability == false
      ? 0
      : parseFloat(((salary.aErnTotalEarning * 3.25) / 100).toFixed(2));
  salary.aErcMedicalInsurance = aErcMedicalInsurance;
  salary.aMonthlyCTC =
    salary.aErcEmployerEPS +
    salary.aErcEmployerEPF +
    salary.aErcAdminCharges +
    salary.aErcEDLICharges +
    salary.aErcEsiEmployer +
    salary.aErcMedicalInsurance +
    salary.aErnTotalEarning;
  return salary;
};

//Updated Calculation for Payslip
export const calculateUpdatedESISalary = (
  aGrossSalary: number,
  aErnConveyance: number,
  aMedicalInsurance: number,
  aErnIncentive: number
) => {
  const esiSalary: EmsUpdatedPfEsiSalary = {} as EmsUpdatedPfEsiSalary;
  esiSalary.aGrossSalary = parseFloat(aGrossSalary.toFixed(2));
  esiSalary.aCTC = parseFloat((esiSalary.aGrossSalary * 12).toFixed(2));
  esiSalary.aErnBasic = parseFloat(
    ((esiSalary.aGrossSalary * 50) / 100).toFixed(2)
  );
  esiSalary.aErnHra = parseFloat(((esiSalary.aErnBasic * 50) / 100).toFixed(2));
  esiSalary.aErnConveyance = parseFloat(aErnConveyance.toFixed(2));
  esiSalary.aDeductEmployerPf =
    esiSalary.aErnBasic > 15000
      ? 1950
      : parseFloat((esiSalary.aErnBasic * 0.13).toFixed(2));
  esiSalary.aDeductESIEmployer =
    esiSalary.aGrossSalary > 21000
      ? 0
      : parseFloat(((esiSalary.aGrossSalary * 3.25) / 100).toFixed(2));
  esiSalary.aErnBasketAllowance = parseFloat(
    (
      esiSalary.aGrossSalary -
      (esiSalary.aErnBasic +
        esiSalary.aErnHra +
        esiSalary.aErnConveyance +
        esiSalary.aDeductEmployerPf +
        esiSalary.aDeductESIEmployer)
    ).toFixed(2)
  );
  if (esiSalary.aErnBasketAllowance < 0) {
    esiSalary.aErnBasketAllowance = 0;
  }
  esiSalary.aEarnedGross = parseFloat(
    (
      esiSalary.aErnBasic +
      esiSalary.aErnHra +
      esiSalary.aErnConveyance +
      esiSalary.aErnBasketAllowance -
      esiSalary.aDeductEmployerPf
    ).toFixed(2)
  );
  esiSalary.aDeductOwnPf =
    esiSalary.aErnBasic > 15000
      ? 1800
      : parseFloat((esiSalary.aErnBasic * 0.12).toFixed(2));
  esiSalary.aDeductPTax = calculatePTax(esiSalary.aGrossSalary);
  esiSalary.aDeductESI =
    esiSalary.aGrossSalary > 21000
      ? 0
      : parseFloat(((esiSalary.aGrossSalary * 0.75) / 100).toFixed(2));
  esiSalary.aNetAmount = parseFloat(
    (
      esiSalary.aEarnedGross -
      (esiSalary.aDeductOwnPf + esiSalary.aDeductPTax + esiSalary.aDeductESI)
    ).toFixed(2)
  );
  esiSalary.aMedicalInsurance = parseFloat(aMedicalInsurance.toFixed(2));
  esiSalary.aErnIncentive = parseFloat(aErnIncentive.toFixed(2));
  return esiSalary;
};

export const calculateUpdatedNonPfSalary = (
  nonPfSalaryStructure: EmsUpdatedNonPfSalary
) => {
  const nonPfSalary: EmsUpdatedNonPfSalary = {} as EmsUpdatedNonPfSalary;
  if (
    nonPfSalaryStructure.aPaidDays != null &&
    nonPfSalaryStructure.aPaidDays !== undefined &&
    nonPfSalaryStructure.aMonthDays !== null &&
    nonPfSalaryStructure.aMonthDays !== undefined
  ) {
    nonPfSalary.aPaidDays = safe(nonPfSalaryStructure.aPaidDays);
    nonPfSalary.aMonthDays = safe(nonPfSalaryStructure.aMonthDays);
    nonPfSalary.aEarnedGross = calculatePerDayWise(
      nonPfSalary.aPaidDays,
      nonPfSalary.aMonthDays,
      nonPfSalaryStructure.aEarnedGross
    );

    nonPfSalary.aErnBasic = calculatePerDayWise(
      nonPfSalary.aPaidDays,
      nonPfSalary.aMonthDays,
      nonPfSalaryStructure.aErnBasic
    );

    nonPfSalary.aErnHra = calculatePerDayWise(
      nonPfSalary.aPaidDays,
      nonPfSalary.aMonthDays,
      nonPfSalaryStructure.aErnHra
    );

    nonPfSalary.aErnConveyance = calculatePerDayWise(
      nonPfSalary.aPaidDays,
      nonPfSalary.aMonthDays,
      nonPfSalaryStructure.aErnConveyance
    );

    nonPfSalary.aErnBasketAllowance = calculatePerDayWise(
      nonPfSalary.aPaidDays,
      nonPfSalary.aMonthDays,
      nonPfSalaryStructure.aErnBasketAllowance
    );
  } else {
    if (nonPfSalaryStructure.dStartDate && nonPfSalaryStructure.dEndDate) {
      nonPfSalary.aPaidDays = calculatePaidDays(
        nonPfSalaryStructure.dStartDate,
        nonPfSalaryStructure.dEndDate
      );
      nonPfSalary.aMonthDays = nonPfSalary.aPaidDays; // When generate payslip aMonthDays=aPaidDays
    }
    nonPfSalary.aEarnedGross = nonPfSalaryStructure.aEarnedGross;
    nonPfSalary.aErnBasic = nonPfSalaryStructure.aErnBasic;
    nonPfSalary.aErnHra = nonPfSalaryStructure.aErnHra;
    nonPfSalary.aErnConveyance = nonPfSalaryStructure.aErnConveyance;
    nonPfSalary.aErnBasketAllowance = nonPfSalaryStructure.aErnBasketAllowance;
  }

  nonPfSalary.aErnIncentive = nonPfSalaryStructure.aErnIncentive;
  nonPfSalary.aPerformanceBonus = nonPfSalaryStructure.aPerformanceBonus;
  nonPfSalary.aMedicalInsurance = nonPfSalaryStructure.aMedicalInsurance;
  nonPfSalary.aTDS = nonPfSalaryStructure.aTDS;
  nonPfSalary.aIncomeTax = nonPfSalaryStructure.aIncomeTax;
  nonPfSalary.aOtherDeduction = nonPfSalaryStructure.aOtherDeduction
    ? nonPfSalaryStructure.aOtherDeduction
    : 0;

  //For Net Amount
  nonPfSalary.aNetAmount =
    safe(nonPfSalary.aEarnedGross) -
    safe(nonPfSalary.aTDS) -
    safe(nonPfSalary.aOtherDeduction) -
    safe(nonPfSalary.aIncomeTax);
  nonPfSalary.aNetAmount = safe(nonPfSalary.aNetAmount);
  const aMonthlyCTC =
    safe(nonPfSalary.aEarnedGross) + safe(nonPfSalary.aMedicalInsurance);
  nonPfSalary.aCTC =
    nonPfSalaryStructure.aCTC && nonPfSalaryStructure.aCTC > 0
      ? safe(nonPfSalaryStructure.aCTC)
      : aMonthlyCTC * 12 +
        safe(nonPfSalary.aErnIncentive) +
        safe(nonPfSalary.aPerformanceBonus);
  nonPfSalary.aCTC = safe(nonPfSalary.aCTC);
  const sanitizedNonPfSalary = sanitizeRoundValue(
    nonPfSalary
  ) as EmsUpdatedNonPfSalary;
  return sanitizedNonPfSalary;
};

export const calculateUpdatedPfSalary = (
  pfSalaryStructure: EmsUpdatedPfSalary
): EmsUpdatedPfSalary => {
  if (hasPaidDaysAndMonthDays(pfSalaryStructure)) {
    calculateSalaryByPerDay(pfSalaryStructure);
  } else if (pfSalaryStructure.dStartDate && pfSalaryStructure.dEndDate) {
    estimateSalaryByDateRange(pfSalaryStructure);
  }

  calculateSpecialAllowances(pfSalaryStructure);
  calculateGross(pfSalaryStructure);
  calculateDeductions(pfSalaryStructure);
  calculateNetAmount(pfSalaryStructure);
  calculateCTC(pfSalaryStructure);

  pfSalaryStructure.isActivePF = pfSalaryStructure.isActivePF;
  pfSalaryStructure.isActiveESI = pfSalaryStructure.isActiveESI;
  const sanitizedValue = sanitizeRoundValue(
    pfSalaryStructure
  ) as EmsUpdatedPfSalary;
  return sanitizedValue;
};

const hasPaidDaysAndMonthDays = (structure: EmsUpdatedPfSalary): boolean => {
  return (
    structure.aPaidDays != null &&
    structure.aMonthDays != null &&
    structure.aPaidDays !== undefined &&
    structure.aMonthDays !== undefined
  );
};

const calculateSalaryByPerDay = (structure: EmsUpdatedPfSalary): void => {
  structure.aPaidDays = safe(structure.aPaidDays);
  structure.aMonthDays = safe(structure.aMonthDays);

  structure.aGrossSalary = calculatePerDayWise(
    structure.aPaidDays,
    structure.aMonthDays,
    structure.aGrossSalary
  );
  structure.aErnBasic = calculatePerDayWise(
    structure.aPaidDays,
    structure.aMonthDays,
    structure.aErnBasic
  );
  structure.aErnHra = calculatePerDayWise(
    structure.aPaidDays,
    structure.aMonthDays,
    structure.aErnHra
  );
  structure.aErnConveyance = calculatePerDayWise(
    structure.aPaidDays,
    structure.aMonthDays,
    structure.aErnConveyance
  );
  structure.aErnBasketAllowance = calculatePerDayWise(
    structure.aPaidDays,
    structure.aMonthDays,
    structure.aErnBasketAllowance
  );
};

const estimateSalaryByDateRange = (structure: EmsUpdatedPfSalary): void => {
  if (structure.dStartDate && structure.dEndDate) {
    structure.aPaidDays = calculatePaidDays(
      structure.dStartDate,
      structure.dEndDate
    );
    structure.aMonthDays = structure.aPaidDays; // When generate payslip aMonthDays=aPaidDays
  }

  structure.aGrossSalary = structure.aGrossSalary;
  structure.aErnBasic = structure.aErnBasic;
  structure.aErnHra = structure.aErnHra;
  structure.aErnConveyance = structure.aErnConveyance;
  structure.aErnBasketAllowance = structure.aErnBasketAllowance;
};

const calculateSpecialAllowances = (structure: EmsUpdatedPfSalary): void => {
  const isSpecial = structure.bSpecialCategory;
  structure.aFPA = isSpecial ? structure.aFPA : 0;
  structure.aSpecialAllowance = isSpecial ? structure.aSpecialAllowance : 0;
  structure.aLTA = isSpecial ? structure.aLTA : 0;
};

const calculateGross = (structure: EmsUpdatedPfSalary): void => {
  structure.aEarnedGross =
    safe(structure.aErnBasic) +
    safe(structure.aErnHra) +
    safe(structure.aErnConveyance) +
    safe(structure.aErnBasketAllowance) +
    safe(structure.aFPA) +
    safe(structure.aSpecialAllowance) +
    safe(structure.aLTA);
};

const calculateDeductions = (structure: EmsUpdatedPfSalary): void => {
  structure.aDeductPTax = calculatePTax(safe(structure.aEarnedGross));

  if (structure.isActivePF) {
    const basic = safe(structure.aErnBasic);
    structure.aDeductOwnPf =
      structure.aDeductOwnPf && structure.bSpecialCategory
        ? safe(structure.aDeductOwnPf)
        : Math.min(basic * 0.12, 1800);
    structure.aDeductEmployerPf =
      structure.aDeductEmployerPf && structure.bSpecialCategory
        ? safe(structure.aDeductEmployerPf)
        : Math.min(basic * 0.13, 1950);
  }

  if (structure.isActiveESI) {
    const gross = safe(structure.aEarnedGross);
    structure.aDeductESI = gross <= 21000 ? (gross * 0.75) / 100 : 0;
    structure.aDeductESIEmployer = gross < 21000 ? (gross * 3.25) / 100 : 0;
  }
  structure.aOtherDeduction = structure.aOtherDeduction;
  structure.aIncomeTax = structure.aIncomeTax;
};

const calculateNetAmount = (structure: EmsUpdatedPfSalary): void => {
  structure.aNetAmount =
    structure.aEarnedGross === 0
      ? 0
      : safe(structure.aEarnedGross) -
        (safe(structure.aDeductOwnPf) +
          safe(structure.aDeductPTax) +
          safe(structure.aDeductESI) +
          safe(structure.aOtherDeduction) +
          safe(structure.aIncomeTax));
};

const calculateCTC = (structure: EmsUpdatedPfSalary): void => {
  structure.aMedicalInsurance = safe(structure.aMedicalInsurance);
  structure.aErnIncentive = safe(structure.aErnIncentive);
  structure.aPerformanceBonus = safe(structure.aPerformanceBonus);

  const monthlyCTC =
    safe(structure.aEarnedGross) +
    safe(structure.aDeductEmployerPf) +
    safe(structure.aDeductESIEmployer) +
    safe(structure.aMedicalInsurance);

  structure.aCTC =
    structure.aCTC && structure.aCTC > 0
      ? safe(structure.aCTC)
      : monthlyCTC * 12 +
        structure.aErnIncentive +
        safe(structure.aPerformanceBonus);
};

const calculatePTax = (aGrossSalary: number): number => {
  if (aGrossSalary <= 10000) return 0;
  else if (aGrossSalary >= 10001 && aGrossSalary <= 15000) return 110;
  else if (aGrossSalary >= 15001 && aGrossSalary <= 25000) return 130;
  else if (aGrossSalary >= 25001 && aGrossSalary <= 40000) return 150;
  else if (aGrossSalary > 40000) return 200;
  else return 0;
};

const safe = (v: number | undefined): number =>
  v !== undefined && v >= 0 ? v : 0;

const calculatePerDayWise = (
  aPaidDays: number,
  aMonthDays: number,
  value: number | undefined
): number => {
  value = safe(value);
  return (value / aMonthDays) * aPaidDays;
};
// const float = (v: number | undefined): number => {
//   v = safe(v);
//   return parseFloat(v.toFixed(2));
// };
const sanitizeRoundValue = (obj: Record<string, any>): Record<string, any> => {
  const sanitized: Record<string, any> = {};
  for (const key in obj) {
    if (Object.prototype.hasOwnProperty.call(obj, key)) {
      const value = obj[key];
      sanitized[key] = typeof value === 'number' ? Math.round(value) : value;
    }
  }

  return sanitized;
};

const calculatePaidDays = (dStartDate: Date, dEndDate: Date): number => {
  const startDate = moment(dStartDate).zone('Asia/Kolkata');
  const endDate = moment(dEndDate).zone('Asia/Kolkata');
  const aPaidDays = endDate.diff(startDate, 'days') + 1;
  return aPaidDays;
};

//For generate the pd slip
export const calculatePfDetails = (inputDetails: InputForPFSlip): PFSlip => {
  const pfDetails: PFSlip = {
    eeDeductionPF: 0,
    employerEPS: 0,
    employerEPF: 0,
    adminCharges: 0,
    eDLICharge: 0,
    eeDeductionESI: 0,
    eSIEmployer: 0,
  } as PFSlip;
  if (inputDetails.isActivePF) {
    const basic12Percent = inputDetails.basic * 0.12;
    pfDetails.eeDeductionPF = basic12Percent < 1800 ? basic12Percent : 1800;
    const basic833Percent = (inputDetails.basic * 8.33) / 100;
    pfDetails.employerEPS = basic833Percent < 1250 ? basic833Percent : 1250;
    const basic367Percent = (inputDetails.basic * 3.67) / 100;
    pfDetails.employerEPF = basic367Percent < 550 ? basic367Percent : 550;
    const basic05Percent = (inputDetails.basic * 0.5) / 100;
    pfDetails.adminCharges = basic05Percent < 75 ? basic05Percent : 75;
    pfDetails.eDLICharge = pfDetails.adminCharges;
  }
  if (inputDetails.isActiveESI) {
    const gross075Percent = (inputDetails.gross * 0.75) / 100;
    pfDetails.eeDeductionESI =
      inputDetails.gross <= 21000 ? gross075Percent : 0;
    const gross325Percent = (inputDetails.gross * 3.25) / 100;
    pfDetails.eSIEmployer = inputDetails.gross < 21000 ? gross325Percent : 0;
  }
  pfDetails.eeDeductionPTax = calculatePTax(inputDetails.gross);
  const sanitizedPfValue = sanitizeRoundValue({
    ...inputDetails,
    ...pfDetails,
  }) as PFSlip;
  return sanitizedPfValue;
};
