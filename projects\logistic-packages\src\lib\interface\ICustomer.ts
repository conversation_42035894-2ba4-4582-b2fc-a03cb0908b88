import { Document, SchemaTimestampsConfig } from "mongoose";
import { IEmployee } from "./IUser";
import { addressType } from "../enums/utilsEnum";

export interface ICustomer extends Document, SchemaTimestampsConfig {
    sName: string,
    sEmail: string,
    aPhoneNo: number,
    sPassword: string,
    sCompanyName: string,
    tAddress?: IAddress[] | string[],
    tAssociate?: IEmployee | string,
    sState?: string,
    bIsDeleted?: boolean,
    bIsActive?: boolean,
    bIsApproved?: boolean,
    sProfileUrl?: string
}
export interface IAddress extends Document {
    sKey: addressType,
    sValue: string
}