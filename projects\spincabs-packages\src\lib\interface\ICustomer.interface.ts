import { Document, SchemaTimestampsConfig } from "mongoose";
import { ICorporate } from "./IAdmin.interface";

interface IBase extends Document, SchemaTimestampsConfig {
}

export interface ICustomer extends IBase {
    bIsCorporateCustomer: boolean, // def false
    tCorporate?: ICorporate | string
    sName: string,
    sCustomerId?: string,
    sEmail: string,
    sPassword?: string,
    aPhoneNo: number,
    dDob?: Date,
    sGender?: string,
    sAddress?: string,
    bLocationSharing: boolean,
    sProfileUrl?: string,
    sDeviceToken?: string,
    bIsActive?: boolean,
    bCanLogin?: boolean,
    bIsDeleted?: boolean,
    aRatings?: number,
}