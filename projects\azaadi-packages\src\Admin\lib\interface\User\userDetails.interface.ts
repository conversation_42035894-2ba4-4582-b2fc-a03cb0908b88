import { Types } from 'mongoose';
import {
  IActiveStatus,
  IConcept,
  Identifiable,
} from '../Common/common.interface';
import { IRole } from '../Organization';
import { IDepartment } from '../Organization/department.interface';
import { IOrganization } from '../Organization/organization.interface';

export interface IUserDetails extends Identifiable, IActiveStatus {
  sName: string;
  tDepartment: IDepartment;
  dJoinDate: string;
  tOrganization: IOrganization;
  tEducationInfo: any[];
  tExperienceInfo: any[];
}
export interface IUserInfo {
  /** Full name of the user */
  sName: string;
  /** Email address of the user */
  sEmail: string;
  /** Hashed password of the user */
  sPassword: string;
  /** ProfilePicture of the user */
  sProfilePicture: string;
  /** Role of the user */
  tRoleId: string | Types.ObjectId | IRole;
  /** EmployeeCode of the user */
  tEmpCode: string;
  /** Status of the user */
  eStatus: string;
  /** Reference to the organization the user belongs to */
  tOrganizationId: string | Types.ObjectId;
  /** Reference to the user's role in the system */
  tDepartmentId: string | Types.ObjectId;
  /** Reference to the user's department */
  sDeviceToken?: string;
}
export interface IUserInfoModel extends IConcept, IUserInfo {}
