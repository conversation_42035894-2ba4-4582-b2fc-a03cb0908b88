export enum TransactionType {
    CREDIT = "Cr",
    DEBIT = "Dr",
    DEBIT_PROCESSING = "Debit processing",
    FAILED = "Failed",
}
export enum PaymentMethodType {
    ONLINE = "Online",
    OFFLINE = "Offline"
}
export enum DriverWithdrawalRequestStatus {
    APPROVED = "Approve",
    PENDING = "Pending",
    REJECTED = "Rejected",
}
export enum FuelType {
    Petrol = "Petrol",
    Diesel = "Diesel",
    Electric = "Electric",
    CNG = "CNG"
}
export enum DocumentsType {
    RC = "Registration Certificate",
    PUC = "Pollution Certificate",
    PERMIT = "Permit Certificate",
    FITNESS = "Fitness Certificate",
    INSURANCE = "Insurance Certificate",
    AADHAAR = "Aadhaar Card",
    PAN = "Pan Card",
    DL = "Driving Licence",
    PROFILE = "Profile Picture",
    VEHICLE_PIC = "Vehicle Picture",
}
export enum ValidationStatus {
    APPROVED = "Approved",
    PENDING = "Pending",
    REJECTED = "Rejected",
}