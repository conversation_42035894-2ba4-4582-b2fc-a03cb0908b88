import { Component, Input, OnInit } from '@angular/core';
import { EmsInputFieldComponent } from '../ems-input-field.component';

@Component({
  selector: 'ems-input-number',
  template: `
    <ng-container>
      <div class="input-field">
        <i class="fas fa-user"></i>
        <input
          [id]="sTags"
          type="number"
          [value]="sDefaultValue"
          [placeholder]="sPlaceholder"
          [disabled]="bIsReadonly"
          [formControl]="_control"
          (input)="onTextValueChange($event)"
        />
        <label for="{{ sTags }}">{{ sLabel }}</label>
      </div>
    </ng-container>
  `,
  styles: [],
})
export class EmsInputNumberComponent extends EmsInputFieldComponent
  implements OnInit {
  @Input() sValue: number | undefined;
  @Input() sDefaultValue: number | undefined;
  constructor() {
    super();
  }
  ngOnInit(): void {
    this.addControlToForm(this.sDefaultValue);
  }

  onTextValueChange(event: Event) {
    var ddd = (event.target as HTMLInputElement).value;
    this._control.setValue(ddd);
    this._control.markAsDirty();
    this._control.updateValueAndValidity();
    this.emsChange.emit(this._control);
  }
}
