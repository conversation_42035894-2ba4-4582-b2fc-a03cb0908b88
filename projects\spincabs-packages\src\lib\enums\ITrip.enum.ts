export enum Gender {
    MALE = 0,
    FEMALE = 1,
    OTHERS = 2
}
export enum Status {
    APPROVED = "approved",
    PENDING = "pending",
    BANNED = "banned"
}
export enum TripStatus {
    CREATE = "Create",
    ACCEPTED = "Accepted",
    PICKEDUP = "Pickedup",
    STARTPICKUP = "Start Pickup",
    COMPLETED = "Completed",
    CANCELLED = "Canceled",
    HALTED = "Halted",
    MOVING = "Moving"
}
export enum ResponseBy {
    DRIVER = "Driver",
    CUSTOMER = "Customer",
}
export enum CarStatus {
    AVAILABLE = "Available",
    ASSIGNED = "Assigned",
    BUSY = "Busy"
}
export enum TripType {
    ONE_WAY_OUTSTATION_TRIP = "One Way Outstation Trip",
    CIRCULAR_OUTSTATION_TRIP = "Circular Outstation Trip",
    RENTAL_TRIP = "Rental Trip",
    INTERCITY_TRIP = "Intercity Trip"
}
export enum TripTypeStatus {
    AVAILABLE = "Available",
    UNAVAILABLE = "Unavailable"
}