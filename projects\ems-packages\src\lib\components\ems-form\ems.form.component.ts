import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormControl, FormGroup } from '@angular/forms';
import { EmsDynamicForm } from '../../interface/dynamicForm.interface';

@Component({
  selector: 'ems-from',
  template: ` <ng-container>
    <div *ngIf="emsDynamicForm" class="_content">
      <div class="_heading">
        <h1 class="_title">{{ emsDynamicForm.sTitle }}</h1>
        <h2 class="_body">{{ emsDynamicForm.sContent }}</h2>
        <div
          class="_group"
          *ngFor="let emsGroup of emsDynamicForm.oInputGroups"
        >
          <div class="_head">
            <h1>{{ emsGroup.sTitle }}</h1>
          </div>
          <ng-container *ngFor="let emsGroupRows of emsGroup.oGroupRows">
            <div class="_row">
              <div
                class="_col"
                *ngFor="let emsInputs of emsGroupRows.oInputFields"
              >
                <ems-input-text
                  *ngIf="
                    emsInputs.sInputType == 'text' ||
                    emsInputs.sInputType == 'password' ||
                    emsInputs.sInputType == 'email' ||
                    emsInputs.sInputType == 'search'
                  "
                  [sPlaceholder]="emsInputs.sPlaceholder"
                  [sLabel]="emsInputs.sLabel"
                  [bIsReadonly]="false"
                  (emsChange)="changeValue($event)"
                  [rootFormGroup]="_form"
                  [sTags]="'id-input'"
                  [sIconUrl]=""
                  [inputType]="emsInputs.sInputType"
                ></ems-input-text>
                <ems-input-select
                  *ngIf="emsInputs.sInputType == 'select'"
                ></ems-input-select>
                <ems-input-checkbox
                  *ngIf="emsInputs.sInputType == 'checkbox'"
                ></ems-input-checkbox>
                <ems-input-radio
                  *ngIf="emsInputs.sInputType == 'radio'"
                ></ems-input-radio>
                <ems-input-number
                  *ngIf="
                    emsInputs.sInputType == 'number' ||
                    emsInputs.sInputType == 'tel'
                  "
                ></ems-input-number>
                <ems-input-date  *ngIf="emsInputs.sInputType == 'date'"></ems-input-date>
              </div>
            </div>
          </ng-container>
        </div>
      </div>
    </div>
  </ng-container>`,
})
export class EmsFormComponent implements OnInit {
  _form: FormGroup;
  @Output() emsSubmit: EventEmitter<EmsDynamicForm> = new EventEmitter();
  @Input() emsDynamicForm: EmsDynamicForm | undefined;
  title = 'ems-package';
  startColumn = 1;
  constructor() {
    this._form = new FormGroup({});
  }
  ngOnInit(): void {}
  changeValue(formsControl: FormControl) {
    console.log(formsControl.value);
  }
}
