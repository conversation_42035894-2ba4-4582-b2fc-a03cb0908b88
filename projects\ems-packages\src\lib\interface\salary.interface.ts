import {
  EmsAdvanceStatus,
  EmsDeductType,
  EmsGovtSub,
  EmsIncentiveType,
  EmsSalaryStructureCategory,
  EmsSkillCategory,
  EmsUpdatedSalaryStructureCategory,
} from '../enums/managements.enum';
import { EmsOrganization } from './manageOrganization.interface';
import { EmsUser } from './user.interface';

//For Previous Salary Structure

export interface EmsNonPfSalary {
  aMonthlyGross: number;
  aBasic?: number;
  aHRA?: number;
  aConveyanceAllowance?: number;
  aBasketAllowance?: number;
  aEarnedGross?: number;
  aAdvanceOpening?: number;
  aFurtherAdvance?: number;
  aAdvanceAdjusted?: number;
  aClosing?: number;
  aMedicalInsurance?: number;
  aTDS?: number;
  aPTax?: number;
  aNetAmount?: number;
  aCTC?: number;
  sName?: string;
  aMonthDays?: number;
  aAbsent?: number;
  aPaidDays?: number;
  _id?: string;
  createdAt?: Date;
  updatedAt?: Date;
  aMiscellaneousAmount?: number;
  sDescription?: string;
}
export interface EmsPfSalary {
  _id?: string;
  createdAt?: Date;
  updatedAt?: Date;
  sName?: string;
  eGovtSub?: EmsGovtSub;
  eCategory?: EmsSkillCategory;
  aMinimumWages?: number;
  bEPFApplicability?: boolean;
  bRestrictPF?: boolean;
  aVoluntaryEPF?: number;
  bESICApplicability?: boolean;
  bPTaxApplicability?: boolean;
  aMBaseRate?: number;
  aMOldBasic?: number;
  aMBasicRate?: number;
  aMHraRate?: number;
  aMConveyance?: number;
  aMBasketAllowance?: number;
  aMGrossRate?: number;
  aErnBasic?: number;
  aErnHra?: number;
  aErnConveyance?: number;
  aErnBasketAllowance?: number;
  aErnGrossEarning?: number;
  aErnIncentive?: number;
  aErnTotalEarning?: number;
  aEedPf?: number;
  aEedPfVoluntary?: number;
  aEedESI?: number;
  aEedPTax?: number;
  aEedOtherDeduction?: number;
  aEedTDS?: number;
  aEedLWF?: number;
  aNetPayable?: number;
  aAdvanceOpening?: number;
  aFurtherAdvance?: number;
  aAdvanceAdjusted?: number;
  aClosing?: number;
  aEpfAbryReimbursement?: number;
  aBankDisbursment?: number;
  aErcEmployerEPS?: number;
  aErcEmployerEPF?: number;
  aErcAdminCharges?: number;
  aErcEDLICharges?: number;
  aErcEsiEmployer?: number;
  aErcAdditional?: number;
  aErcMedicalInsurance?: number;
  aMonthlyCTC?: number;
  dDob?: Date;
  aMonthDays?: number;
  aAbsent?: number;
  aPaidDays?: number;
  aMiscellaneousAmount?: number;
  sDescription?: string;
}

//Updated Salary Structure

export interface EmsUpdatedSalary {
  _id?: string;
  createdAt?: Date;
  updatedAt?: Date;
  aErnBasic?: number;
  aErnHra?: number;
  aErnConveyance?: number;
  aErnBasketAllowance?: number;
  aMedicalInsurance?: number;
  aEarnedGross?: number;
  aOtherDeduction?: number;
  aCTC?: number;
  aNetAmount?: number;
  aErnIncentive?: number;
  aMonthDays?: number;
  aPaidDays?: number;
  aIncomeTax?: number;
  dStartDate?: Date;
  dEndDate?: Date;
  aPerformanceBonus?: number;
}
export interface EmsUpdatedNonPfSalary extends EmsUpdatedSalary {
  aTDS?: number;
}

export interface EmsUpdatedPfSalary extends EmsUpdatedSalary, ActivePfEsi {
  aGrossSalary?: number;
  aDeductOwnPf?: number;
  aDeductEmployerPf?: number;
  aDeductPTax?: number;
  aDeductESI?: number;
  aDeductESIEmployer?: number;
  bSpecialCategory?: boolean;
  aFPA?: number;
  aSpecialAllowance?: number;
  aLTA?: number;
}
export interface ActivePfEsi {
  isActiveESI: boolean;
  isActivePF: boolean;
}
export interface EmsUpdatedPfEsiSalary extends EmsUpdatedPfSalary {
  // aDeductESI?: number;
  // aDeductESIEmployer?: number;
}

// export interface EmsUpdatedPfWithTdsSalary extends EmsUpdatedPfSalary {
//   aTDS?: number;
// }

export interface EmsSalaryStructure {
  _id?: string;
  createdAt?: Date;
  updatedAt?: Date;
  tIdUser?: EmsUser | string;
  tSalaryStructure?:
    | any
    | EmsNonPfSalary
    | EmsPfSalary
    | EmsUpdatedNonPfSalary
    | EmsUpdatedPfSalary
    | EmsUpdatedPfEsiSalary;
  eType?: EmsSalaryStructureCategory | EmsUpdatedSalaryStructureCategory;
  bHavingAdvancePayment?: boolean;
}
export interface EmsUpdatedSalaryStructure extends EmsSalaryStructure {
  tOrganization?: EmsOrganization | string;
  isIncrement?: boolean;
  isHold?: boolean;
  isStopped?: boolean;
}
export interface EmsPayslip extends EmsSalaryStructure {
  aMonth: number;
  aYear: number;
  aSalaryDay?: number;
  dStartDate?: Date;
  dEndDate?: Date;
  bIsReportingAuthApproved?: boolean;
  bIsHrApproved?: boolean;
  aMiscAmount?: number;
  aMiscDescription?: string;
  user?: {
    tIdUser?: string;
    sEmail?: string;
    aPhoneNumber?: string;
    sProfileUrl?: string;
    sName?: string;
    aAadharNo?: string;
    sPfNo?: string;
    sEsiNo?: string;
    dJoinDate?: Date;
    dInactiveDate?: Date;
    tDepartment?: {
      sName?: string;
      sId?: string;
    };
    tDesignation?: {
      sName?: string;
      sId?: string;
    };
    tIdEmployee?: {
      sId?: string;
      aPunchId?: number;
      sCode?: string;
    };
    tRole?: {
      sName?: string;
      sId?: string;
      tOrganization?: {
        sId?: string;
        sName?: string;
        sTag?: string;
      };
    };
  };
  userBank?: {
    sId?: string;
    sBankName?: string;
    sBankAccount?: string;
    sIfscCode?: string;
    sBranchName?: string;
  };
  leaveBalance?: [
    {
      aCount?: number;
      sType?: string;
      sRoleId?: string;
    }
  ];
  _id?: string;
  createdAt?: Date;
  updatedAt?: Date;
  salaryStructureTemplate: EmsSalaryStructure | EmsUpdatedSalaryStructure;
  isHold?: boolean;
}
export interface EmsDiffSalarySheet {
  _id?: string;
  aMonth: number;
  aYear: number;
  aTotalAmount: number;
  aAmountDifference?: number;
  aTotalEmployees?: number;
  tEmployeesJoin?: EmsUser[] | string[];
  tEmployeesLeft?: EmsUser[] | string[];
  tApprovedBy?: EmsUser | string;
  tOrganization?: EmsOrganization | string;
  createdAt?: Date;
  updatedAt?: Date;
}
export interface EmsIncentive {
  _id?: string;
  tIdUser?: EmsUser | string;
  dStartDate?: Date;
  dEndDate?: Date;
  aTotalAmount?: number;
  aPeriodAmount?: number;
  tPeriodicDetails: IncentivePeriodicDetails[];
  tOrganization?: EmsOrganization | string;
  eType?: EmsIncentiveType | string;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface IncentivePeriodicDetails {
  aMonth: number;
  aYear: number;
  aAmount: number;
}

export interface EmsIncentiveDetails {
  tIdUser: EmsUser | string;
  eType: EmsIncentiveType | string;
  aTotalAmount: number;
  dStartDate: Date;
  dEndDate: Date;
  dDate: Date;
  aAmount: number;
  tOrganization?: EmsOrganization | string;
}
export interface EmsAdvanceRequest {
  _id?: string;
  createdAt?: Date;
  updatedAt?: Date;
  tIdUser: EmsUser | string;
  aTotalAmount: number;
  eStatus: EmsAdvanceStatus | string;
  eDeductType?: EmsDeductType | string;
  dStartDate?: Date;
  dEndDate?: Date;
  tOrganization?: EmsOrganization | string;
  monthlyDetails: [
    {
      aMonth?: number;
      aYear?: number;
      aAmount?: number;
      bIsDeducted?: boolean;
    }
  ];
  tHrApprovedBy?: EmsUser | string;
  tAccApprovedBy?: EmsUser | string;
  dApproveDate?: Date;
  dDisbursementDate?: Date;
  sReason?: string;
  sReply?: string;
  tAttachments?: [
    {
      sFilename?: string;
      sPath?: string;
    }
  ];
}

export interface Salary {
  _id?: string;
  tSalary: EmsUpdatedSalaryStructure | EmsSalaryStructure;
  dStartDate: Date;
  dEndDate: Date;
  createdAt?: Date;
  updatedAt?: Date;
}
export interface EmsSalaryHistory {
  _id?: string;
  createdAt?: Date;
  updatedAt?: Date;
  tIdUser: string | EmsUser;
  tSalaries: Salary[];
  tOrganization: string | EmsOrganization;
}
export interface PFSlip extends InputForPFSlip {
  eeDeductionPF: number;
  eeDeductionESI: number;
  eeDeductionPTax: number;
  employerEPS: number;
  employerEPF: number;
  adminCharges: number;
  eDLICharge: number;
  eSIEmployer: number;
}
export interface InputForPFSlip extends ActivePfEsi {
  name: string;
  basic: number;
  hra: number;
  conveyance: number;
  basket: number;
  gross: number;
  disbursement: number;
}
