import { SchemaTimestampsConfig } from "mongoose"
import { ICustomer } from "./ICustomer"
import { IConsignment, IOrderItem } from "./IConsignment"

export interface IReport extends Document, SchemaTimestampsConfig {
    aReportId?: number,
    sTitle?: String,
    sMessage?: String,
    tIdUser: ICustomer | string,
    tConsignment?: IConsignment | string,
    tItems?: IOrderItem[] | string[],
    ePriority?: String,
    sUrls?: String[],
    bIsSolved?: Boolean,
    tReply?: IReply[] | string[],
}

export interface IReply extends Document {
    sReply: String,
    tReplyBy: string,
    replyDateTime: Date
}
