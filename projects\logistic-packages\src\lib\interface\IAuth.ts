export interface IAuth {
    _id?: string;
    aPhoneNo?: number;
    sEmail?: string;
    sPassword?: string;
    bIsActive?: boolean;
    bIsDeleted?: boolean;
    bIsApproved?: boolean;
    aOtp?: number;
}
export interface IPassword {
    _id?: string;
    sEmail: string;
    aPhoneNo?:number;
    sOldPassword?: string;
    sNewPassword: string;
    sResetUiLink?: string;
}
export interface IResetPassword {
    sToken: string;
    sNewPassword: string
}
export interface IVerificationDocument {
    _id: string;
    sIdProof?: string;
    sAddressProof?: string;
    sDrivingLicense?: string;
}
export interface ISignUpResponse<T> {
    data: T;
    authenticate?: boolean;
    sToken?: string
}
export interface IAuthResponse<T> {
    data: T;
    sToken?: string;
}
export interface IOtp {
    aPhoneNo?: number;
    sEmail?: string;
    aOtp?: number;
}