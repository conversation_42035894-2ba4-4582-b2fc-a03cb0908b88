export enum Gender {
    MALE = 0,
    FEMALE = 1,
    OTHERS = 2
}
export enum Status {
    ORDERED = 0,
    INPROGRESS = 1,
    PICKEDUP = 2,
    DELIVERED = 3,
}
export enum addressType {
    BILLINGADDRESS = 0,
    NONDAIRYDELIVERYADDRESS = 1,
    DRYDELIVERYADDRESS = 2
}
export enum AuthType {
    BASIC = 0,
    NTLM = 1
}
export enum PriorityType {
    HIGH = 0,
    MEDIUM = 1,
    LOW = 2
}
export enum NotificationCategory {
    DRIVER = "Driver",
    CUSTOMER = "Customer",
    WAREHOUSE = "Warehouse",
    ADMIN = "Admin"
}
export enum SettingsKey{
    TEMPERATURE="Temperature Threshold",
    HALTED_SINCE="Halted Since Threshold",
    TRIGGER_TIME="Triger Time"
}
