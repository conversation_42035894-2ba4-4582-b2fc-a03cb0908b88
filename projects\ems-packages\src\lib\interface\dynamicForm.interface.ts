import { EmsUser } from './user.interface';

export interface EmsDynamicForm {
    _id?: string;
    sTitle?: string;
    sBgUrl?: string;
    sContent?: string;
    oInputGroups?: EmsFormGroup[];
    tUser?: string | EmsUser;
    createdAt?: Date;
    updatedAt?: Date;
    bIsActive?: boolean;
    bIsPublish?: boolean;
    sTag?: string;
}

export interface EmsFormGroup  {
    sTitle?: string;
    oGroupRows?: EmsGroupRows[];
}
export interface  EmsGroupRows{
    sTitle?: string;
    oInputFields?: EmsInputField[];
}

export interface EmsInputField {
    _id?: string;
    sParentId?: string;
    sLabel?: string;
    sInputType?: string;
    bIsReadonly?: boolean;
    bIsDisabled?: boolean;
    bIsMandatory?: boolean;
    sPlaceholder?: string;
    sIconUrl?: string;
    sTooltips?: string;
}

/**
 * email / text / search / password
 *
 * @export
 * @interface EmsInputFieldText
 * @extends {EmsInputField}
 */
export interface EmsInputFieldText extends EmsInputField {
    sDefaultValue?: string;
    sValue?: string;
}

/**
 * number / tel /range
 *
 * @export
 * @interface EmsInputFieldNumber
 * @extends {EmsInputField}
 */
export interface EmsInputFieldNumber extends EmsInputField {
    aDefaultValue?: number;
    aMin?: number;
    aMean?: number;
    aMax?: number;
}

/**
 * Date / Date-Time / Time
 *
 * @export
 * @interface EmsInputFieldDate
 * @extends {EmsInputField}
 */
export interface EmsInputFieldDate extends EmsInputField {
    sDefaultValue?: Date;
    sValue?: Date;
}

/**
 * checkbox / radio
 *
 * @export
 * @interface EmsInputFieldCheck
 * @extends {EmsInputField}
 */
export interface EmsInputFieldCheck extends EmsInputField {
    sDefaultValue?: string;
}

/**
 * Select
 *
 * @export
 * @interface EmsInputFieldSelect
 * @extends {EmsInputField}
 */
export interface EmsInputFieldSelect extends EmsInputField {
    sDefaultValue?: string;
    oOption: EmsSelectOption[];
}

export interface EmsSelectOption extends EmsInputField {
    _id?: string;
    sName?: string;
    sValue?: string;
    bIsDisabled?: boolean;
    sIconUrl?: string;
}
