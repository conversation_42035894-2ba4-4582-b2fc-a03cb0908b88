import { Document, SchemaTimestampsConfig } from "mongoose";
import { ICustomer } from "./ICustomer.interface";
import { ICar, ICarType, IDriver } from "./IDriver.interface";
import { CarStatus, ResponseBy, TripStatus, TripType, TripTypeStatus } from "../enums/ITrip.enum";
import { ICustomerCoupon } from "./IDiscount.interface";


interface IBase extends Document, SchemaTimestampsConfig {
}
export interface ITrip extends IBase {
    sBookingId: string,
    tType: TripType | string,
    tCustomer: ICustomer | string,
    tDriver: IDriver | string,
    tCar: ICar | string,
    aCountPassenger: number,
    aPrice: number,
    eStatus?: TripStatus | string,
    tCanceledBy?: ResponseBy | string,
    bIsCancelCleared: boolean,
    dTripDateTime?: Date,
    dTripEndDateTime: Date,
    sStartLong: string,
    sStartLat: string,
    sStartLocation: string,
    sEndLong: string,
    sEndLat: string,
    sEndLocation: string,
    aDistance: number,  // in meter
    aDuration: number   // in minute
    tCoupon?: ICustomerCoupon | string,
    tRequestedCars: IOnlineCarStatus[] | string[],
    aStartOtp?: string,
    aCompleteOtp?: string,
}
export interface IBillingAck extends IBase {
    tTrip: ITrip | string,
    tCustomer: ICustomer | string,
    tDriver: IDriver | string,
    aTotalAmount: number,
    aBookingCharge?: number,    // Total Fare
    aPhaseOneDistance?: number, // in Meter
    aPhaseOneFare?: number,     // Total Fare
    aPhaseTwoDistance?: number, // in meter
    aPhaseTwoFare?: number,     // Total Fare
    aPhaseThreeFare?: number,   // Total Fare
    aMinuteFare?: number,       // Total Fare
    aPreviousCancelCharge?: number,
    tCouponCode?: ICustomerCoupon | string,
    aCorporateDiscount?: number
}
export interface IOutstationTrip extends IBase {
    sTripType: TripType
    tCustomer: ICustomer,
    tDriver: IDriver,
    tRequestedDrivers: IDriver[],
    tCar: ICar,
    tCarType: ICarType,
    tCoupon: ICustomerCoupon,
    aCountPassenger: number,
    aPrice: number,
    eStatus: TripStatus,
    tCanceledBy: ResponseBy,
    bIsCancelCleared: boolean,
    dTripDateTime: Date,
    dTripEndDateTime: Date,
    dScheduleDate: Date,
    dHaltStartDateTime: Date,
    dHaltEndDateTime: Date,
    sStartLocation: string,
    sDropLocation: string,
    sEndLocation: string,
    sStartLong: string,
    sStartLat: string,
    sHaltLong: string,
    sHaltLat: string,
    sEndLong: string,
    sEndLat: string,
    aDistance: number,
    aDuration: number,
    sDescription: string,
    aStartOtp?: string,
    aCompleteOtp?: string,
}
export interface IOutstationBillingAck extends IBase {
    tTrip: IOutstationTrip,
    tCustomer: ICustomer,
    tDriver: IDriver,
    aAdvanceAmount: number,
    aPhaseOneDistance: number,
    aBookingCharge: number,
    aPhaseOneFare: number,
    aPhaseTwoDistance: number,
    aPhaseTwoFare: number,
    aPhaseThreeFare: number,
    aDriverCharge: number,
    aDriverAccomodation: number,
    aMinuteFare: number,
    aPreviousCancelCharge: number,
    aHaltCharge: number,
    tCouponCode: ICustomerCoupon,
    aTax: number,
    aTotalAmount: number
}
export interface IOnlineCarStatus extends IBase {
    tCar: ICar | string,
    eStatus: CarStatus | string,
    tTrip?: ITrip | string
}
export interface IRatingsReview extends IBase {
    tTrip: ITrip | string,
    eReviewedBy: ResponseBy | string,
    aRating: number,
    sReview: string,
}
export interface IAvailableTripType extends IBase {
    eType: TripType,
    eStatus: TripTypeStatus
}