import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { BrowserModule } from '@angular/platform-browser';
import { EmsPackagesModule } from 'projects/ems-packages/src/public-api';
// import { EmsPackagesModule } from 'ems-packages';

import { AppRoutingModule } from './app-routing.module';
import { AppComponent } from './app.component';

@NgModule({
  declarations: [AppComponent],
  imports: [
    BrowserModule,
    AppRoutingModule,
    FormsModule,
    ReactiveFormsModule,
    EmsPackagesModule,
  ],
  providers: [],
  bootstrap: [AppComponent],
})
export class AppModule {}
