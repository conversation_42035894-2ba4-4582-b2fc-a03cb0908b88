import { EmsQuestion } from './question.interface';
import { EmsTest } from './test.interface';
import { EmsUser } from '../user.interface';
import { EmsExam } from './exam.interface';
import { EmsRcmUser } from '../RCM/rcm.user.interface';

export interface EmsResult {
  _id?: string;
  tUser?: string | EmsUser;
  tGuestUser?: string | EmsRcmUser;
  tTest?: string | EmsTest;
  tExam?: string | EmsExam;
  bComplete?: boolean;
  aTestTotal?: number;
  aPassMark?: number;
  aUserTotal?: number;
  aPercentage?: number;
  bStatus?: boolean;
  dTestDate?: Date;
  createdAt?: Date;
  updatedAt?: Date;
  tQuestionAnswer?: EmsQuestion[];
  sMetaData?: any[];
}
