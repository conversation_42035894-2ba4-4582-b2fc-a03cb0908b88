import { Document, ObjectId, SchemaTimestampsConfig } from "mongoose";
import { SettingsKey } from "../enums/IAdmin.enum";
interface IBase extends Document, SchemaTimestampsConfig {
}

export interface ISettings extends IBase {
    sKey: SettingsKey | string,
    sValue: any
}
export interface ICity extends IBase {
    sName: string,
    sDescription?: string,
}
export interface IPincode extends IBase {
    sPincode: number,
    tCity?: ICity | string,
}