import { Document, SchemaTimestampsConfig } from "mongoose";
import { IEmployee } from "./IAdmin.interface";
import { TransactionType } from "../enums/IDriver.enum";
interface IBase extends Document, SchemaTimestampsConfig {
}
export interface IAgentWallet extends IBase {
    tAgent: IEmployee | string,
    aAmount: number,
}
export interface IAgentWalletTransaction extends IBase {
    dTransactionDate: Date,
    tAgent: IEmployee | string,
    tAgentWallet?: IAgentWallet | string,
    eType: TransactionType | string,  // CR|DR
    sTransactionId?: string,
    aAmount: number,
    sMessage?: string,
}