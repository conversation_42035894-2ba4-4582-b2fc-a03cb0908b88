import { Component, EventEmitter, Input, Output } from '@angular/core';
import { FormControl, FormGroup } from '@angular/forms';

@Component({
  selector: 'ems-input-field',
  template: ``,
})
export class EmsInputFieldComponent {
  @Input() rootFormGroup?: FormGroup;
  @Input() sParentId?: string;
  @Input() sLabel?: string;
  @Input() bIsReadonly: boolean = false;
  @Input() bIsMandatory?: boolean;
  @Input() sPlaceholder?: string;
  @Input() sIconUrl?: string;
  @Input() sTooltips?: string;
  @Input() sTags: string = '';
  @Output() emsChange: EventEmitter<FormControl> = new EventEmitter();
  _control: FormControl;
  constructor() {
    this._control = new FormControl();
  }

  protected addControlToForm(initialValue: any) {
    Promise.resolve(null).then(() => {
      this._control.setValue(initialValue);
      // this._control.setValidators(this._validators);
      if (this.rootFormGroup && this.sTags != '') {
        this.rootFormGroup.addControl(this.sTags, this._control);
      }
      this._control.updateValueAndValidity();
    });
  }
}
