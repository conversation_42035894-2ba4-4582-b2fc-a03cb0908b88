import { Document, SchemaTimestampsConfig } from "mongoose";
import { AuthType } from "../enums/utilsEnum";

interface IBase extends Document, SchemaTimestampsConfig {
    sName?: string;
    sTag?: string;
}

export interface IUserGroup extends IBase {
    tAccess?: IManageAccess | string;
}
export interface IBranch extends IBase {
}
export interface IDepartment extends IBase {
    sDescription?: string
}

export interface IDesignation extends IBase {
    sDescription?: string;
}
export interface IManageAccess extends IBase {
    sType?: string;
    sDescription?: string
}
export interface IUserRole extends IBase {
}
export interface IExternalAPI extends IBase {
    sUrl: string;
    sAuthType: AuthType;
    sDomain: string;
    sUserName: string;
    sPassword: string;
}