import { TicketImpactCategory } from '../../enums';
import { ITicket } from '../Ticket';
import { IUserInfo } from '../User';
import { IDocumentDTO } from './document.dto.interface';
import { IUserInfoDTO } from './userInfo.dto.interface';

/**
 * DTO Interface for Ticket
 */
export interface ITicketDTO
  extends Omit<
    ITicket,
    | 'tRequester'
    | 'tImpactUser'
    | 'tApprover'
    | 'tAssignedTo'
    | 'tObserver'
    | 'aDocuments'
  > {
  /** User who requested the ticket */
  tRequester?: string | IUserInfoDTO;

  /** User impacted by the ticket */
  tImpactUsers?: string[] | IUserInfoDTO[];

  /** User who approved the ticket */
  tApprover?: string | IUserInfoDTO;

  /** User assigned to handle the ticket */
  tAssignedTo?: string | IUserInfoDTO;

  /** Observer(s) of the ticket */
  tObserver?: string[] | IUserInfoDTO[];

  /** Array of attached documents */
  aDocuments?: string[] | IDocumentDTO[];
}

export interface IAllTicketDTO {
  tickets: ITicketDTO[];
  count: number;
}

export interface ITicketReferenceDTO {
  /** Reference to the ticket */
  tTicketId: string | ITicketDTO;
}

// Observer DTO
export interface ITicketObserverDTO extends ITicketReferenceDTO {
  /** Observers of the ticket */
  tObserver: string[] | IUserInfoDTO[];
}

// Impact DTO
export interface ITicketImpactDTO extends ITicketReferenceDTO {
  /** Users impacted by the ticket */
  tImpactUser: string[] | IUserInfo[];
}

export interface ICreateTicketTemplateDTO {
  tOrganization: string;
  tRequester: string;
  eStatusKey: string; // "OPEN" | "IN_PROGRESS" | "CLOSED"
  eCategoryKey: string;
  eUrgency: string;
  eImpactCategory: TicketImpactCategory;
  tImpactUser: string;
  tApprover: string;
  tAssignedTo: string;
  tObserver: string[];
  tAsset: string;
  sDescription: string;
  sTitle: string;
  dIncidentDate: Date;
  aDocuments: string[];
}
export interface ITicketTemplateDTO {
  department: string;
  searchText?: string;
  endDate?: string;
  startDate?: string;
  priority?: TicketSortPriority;
}

export type TicketSortPriority = {
  _id?: 1 | -1;
  sTitle?: 1 | -1;
  eUrgency?: 1 | -1;
  dIncidentDate?: 1 | -1;
  'tRequester.sName'?: 1 | -1;
  'tAssignedTo.sName'?: 1 | -1;
};
