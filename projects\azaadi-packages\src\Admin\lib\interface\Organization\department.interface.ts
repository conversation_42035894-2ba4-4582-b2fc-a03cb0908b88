import { Types } from 'mongoose';
import { IConcept } from '../Common/common.interface';
import { IUserInfo } from '../User';
import { IOrganization } from './organization.interface';

export interface IDepartment {
  /** Name of the department */
  sName: string;
  /** Reference to the department head */
  tDepartmentHead: string | Types.ObjectId | IUserInfo;
  /** Reference to the parent organization */
  tOrganization: string | Types.ObjectId | IOrganization;
}
export interface IDepartmentModel extends IConcept, IDepartment {}
