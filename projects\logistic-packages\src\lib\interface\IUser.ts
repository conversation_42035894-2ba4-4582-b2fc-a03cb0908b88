import { Document, SchemaTimestampsConfig } from "mongoose";
import { Gender } from "../enums/utilsEnum";
import { IBranch, IDepartment, IDesignation, IManageAccess, IUserGroup, IUserRole } from "./IAdmin";

export interface IEmployee extends Document, SchemaTimestampsConfig {
    sName: string;
    sEmail: string;
    aPhoneNo: number;
    sPassword: string;
    eGender: Gender;
    dDob: Date;
    sPresentAddress: string;
    tUserRole: [IUserRole] | string[];
    tBranch: IBranch | string;
    tManageAccess: [IManageAccess] | string[];
    tDesignation: IDesignation | string;
    tDepartment?: IDepartment | string;
    tUserGroup?: IUserGroup | string;
    tReportingAuth?: IEmployee | string;
    bIsActive: boolean;
    bIsDeleted: boolean;
}
export interface IEmployeeLog extends Document, SchemaTimestampsConfig {
    sMetaKey: string;
    sMetaValue: string;
    tIdUser: IEmployee | string
}