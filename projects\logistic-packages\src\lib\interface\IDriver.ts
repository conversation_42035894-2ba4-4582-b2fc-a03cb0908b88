import { Document, SchemaTimestampsConfig } from "mongoose";
import { Gender } from "../enums/utilsEnum";
import { IBranch } from "./IAdmin";
import { IEmployee } from "./IUser";

export interface IDriver extends Document, SchemaTimestampsConfig {
    sName: string,
    aPhoneNo: number,
    aAltNo?: number,
    sPresentAddress?: string,
    sPermanentAddress?: string,
    sEmail?: string,
    eGender?: Gender,
    dDob?: Date,
    tBranches?: IBranch | string,
    bIsApproved?: boolean,
    tApprovedBy?: IEmployee | string,
    bIsActive?: boolean,
    bIsDeleted?: boolean,
    sCarNo?: string,
    sIdProof?: string,
    sAddressProof?: string,
    sDrivingLicense?: string,
    tCreatedBy?: IEmployee | string,
    sLocationCode?: string,
    sProfileUrl?:string
}

export interface IVehicle extends Document, SchemaTimestampsConfig {
    sType?: string,
    sRegNo: string,
    sName: string,
    sModel: string,
    sCompany: string,
    sDescription?: string,
    aCapacity: number,
    tCreatedBy?: IEmployee | string,
    tDriver?: IDriver | string
}
export interface IVehicleData extends Document, SchemaTimestampsConfig {
    systime: number,
    exceptionBM: number,
    virtualName: string,
    speed: number,
    direction: number,
    haltedSince: string,
    elevation: number,
    distance: number,
    locStr: string,
    noDataSince: string,
    analogData: string,
    latitude: number,
    movingSince: string,
    longitude: number,
    regNo: string,
    bmStr: string,
    tCreatedBy: IEmployee | string
}

export interface IVehicleLocation extends Document, SchemaTimestampsConfig {
    systime: number,
    exceptionBM: number,
    virtualName: string,
    speed: number,
    direction: number,
    haltedSince: string,
    elevation: number,
    Temperature: number,
    timestamp: number,
    distance: number,
    locStr: string,
    noDataSince: string,
    analogData: string,
    lattitude: number,
    movingSince: string,
    longitude: string,
    regNo: string,
    bmStr: string
}