import { EmsInputCheckboxComponent } from './components/fields/ems-input-checkbox/ems.input.checkbox.component';
import { NgModule } from '@angular/core';
import { EmsInputTextComponent } from './components/fields/ems-input-text/ems-input-text.component';
import { EmsInputFieldComponent } from './components/fields/ems-input-field.component';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { EmsFormComponent } from './components/ems-form/ems.form.component';
import { CommonModule } from '@angular/common';
import { EmsInputNumberComponent } from './components/fields/ems-input-number/ems-input-number.component';
import { EmsInputSelectComponent } from './components/fields/ems-input-select/ems.input.select.component';
import { EmsInputDateComponent } from './components/fields/ems-input-date/ems.input.date.component';
import { EmsInputRadioComponent } from './components/fields/ems-input-radio/ems.input.radio.component';
import { EmsInputRangeComponent } from './components/fields/ems-input-range/ems.input.range.component';


@NgModule({
  declarations: [
    EmsInputFieldComponent,
    EmsFormComponent,
    EmsInputCheckboxComponent,
    EmsInputDateComponent,
    EmsInputNumberComponent,
    EmsInputRadioComponent,
    EmsInputRangeComponent,
    EmsInputSelectComponent,
    EmsInputTextComponent,

  ],
  imports: [FormsModule, ReactiveFormsModule, CommonModule],
  exports: [
    EmsFormComponent,
    EmsInputCheckboxComponent,
    EmsInputDateComponent,
    EmsInputNumberComponent,
    EmsInputRadioComponent,
    EmsInputRangeComponent,
    EmsInputSelectComponent,
    EmsInputTextComponent,
  ],
})
export class EmsPackagesModule {}
