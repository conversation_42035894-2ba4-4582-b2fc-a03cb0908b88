import { EmsFileType } from '../enums/managements.enum';
import {
  EmsAttendanceStatus,
  EmsAttendanceType,
  EmsUserLeaveStatus,
  EmsUserRequestStatus,
} from '../enums/users.enum';
import { EmsEmployeeCode } from './employeeCode.interface';
import {
  EmsBranches,
  EmsDepartments,
  EmsDesignation,
  EmsManageAssets,
  EmsManageDocuments,
  EmsManageLeave,
  EmsManageUserRequest,
  EmsOrganization,
  EmsRole,
  EmsShift,
} from './manageOrganization.interface';
import {
  EmsSalaryStructure,
  EmsUpdatedSalaryStructure,
} from './salary.interface';

/**
 * User Doc
 *
 * @export
 * @interface User
 */
export interface EmsUser {
  _id?: string;
  tIdEmployee?: EmsEmployeeCode | string;
  sPassword?: string;
  sEmail?: string;
  aPhoneNumber?: number;
  sWorkingType?: string;
  sProfileUrl?: string;
  tBranches?: EmsBranches[] | string[];
  tUserDetails?: EmsUserDetails;
  tRole: EmsRole | string;
  createdAt?: Date;
  updatedAt?: Date;
  bOnlyOfficePunch?: boolean;
  bIsActive?: boolean;
  bCanLogin?: boolean;
  tShift?: EmsShift[] | string[];
  bIsApplicableAdvance?: boolean;
  dInactiveDate?: Date;
  bIsResigned?: boolean;
  bIsCreatedBySuperAdmin?: boolean;
  bIsPermanentWFH?: boolean;
}
export interface EmsUserDetails {
  _id?: string;
  tIdUser?: string | EmsUser;
  sName: string;
  tDepartment?: string | EmsDepartments;
  tDesignation?: string | EmsDesignation;
  aAltPhoneNo?: number;
  sPersonalEmail?: string;
  sGender?: string;
  dDob?: Date;
  dJoinDate?: Date;
  sPresentAddress?: string;
  sPermanentAddress?: string;
  sFatherName?: string;
  aFatherMobileNo?: string; // First it was number, but now it is string
  aAadharNo?: number;
  sPanNo?: string;
  createdAt?: Date;
  updatedAt?: Date;
  tEducationInfo?: EmsEducationInfo[];
  tExperienceInfo?: EmsExperienceInfo[];
  sPassportNo?: string;
  sPassportExpDate?: Date;
  sNationality?: string;
  sReligion?: string;
  sMaritalStatus?: string;
  sEmploymentSpouse?: string;
  aNoChildren?: number;
  bIsActive?: boolean;
  tOrganization?: string | EmsOrganization;
  sUserApplyType?: string;
  metaData?: string[];
  tSalaryStructure?: EmsSalaryStructure | string;
  tUpdatedSalaryStructure?: EmsUpdatedSalaryStructure | string;
  dLeaveDate?: Date;
  sPfNo?: string;
  sEsiNo?: string;
  sMotherName?: string;
  aEmergencyContactNo?: number;
  sEmergencyContactName?: string;
}

export interface EmsUserLog {
  _id?: string;
  tIdUser: string | EmsUser;
  createdAt: Date;
  updatedAt?: Date;
  sMetaKey: string;
  sMetaValue: string;
  tOrganization: string | EmsOrganization;
}

export interface EmsUserDocuments {
  _id?: string;
  tIdUser: string | EmsUser;
  sName: string;
  tManageDoc: string | EmsManageDocuments;
  createdAt?: Date;
  updatedAt?: Date;
  tDocumentDetails?: EmsUserDocumentDetails[];
}

export interface EmsUserDocumentDetails {
  _id?: string;
  sName?: string;
  sSize?: string;
  sUrl?: string;
  eType?: EmsFileType;
  bStatus?: boolean;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface EmsUserAssets {
  _id?: string;
  sName: string;
  tIdUser: string | EmsUser;
  tManageAsset: string[] | EmsManageAssets[];
  createdAt?: Date;
  updatedAt?: Date;
}

export interface EmsEducationInfo {
  _id?: string;
  sName?: string;
  sCourse?: string;
  dStartDate?: Date;
  bPursuing?: boolean;
  dEndDate?: Date;
  aTotalMarks?: number;
  aObtainMarks?: number;
  createdAt?: Date;
  updatedAt?: Date;
}
export interface EmsExperienceInfo {
  _id?: string;
  sName?: string;
  sDepartment?: string;
  sDesignation?: string;
  sAddress?: string;
  bCurrent?: boolean;
  dStartDate?: Date;
  dEndDate?: Date;
  createdAt?: Date;
  updatedAt?: Date;
}
export interface EmsUserLocationTracker {
  sLongitude: string;
  sLatitude: string;
  sTimestamp: string;
  sLocation: string;
}
export interface EmsUserAttendance {
  _id?: string;
  tIdUser: string | EmsUser;
  dStartDate: Date[];
  dEndDate: Date[];
  eAttendanceType?: EmsAttendanceType;
  eEndAttendanceType?: EmsAttendanceType;
  tShift?: EmsShift | string;
  eStatus?: EmsAttendanceStatus;
  sLongitude?: string;
  sLatitude?: string;
  createdAt?: Date;
  updatedAt?: Date;
  aLateDuration?: number; // minute
  sWorkingHours?: string;
  tActivityTracker?: EmsUserLocationTracker[];
}
export interface EmsUserLeaves {
  _id?: string;
  tIdUser?: string | EmsUser;
  dStartDate?: Date;
  dEndDate?: Date;
  tLeaveType?: string | EmsManageLeave;
  tApprovedBy?: string | EmsUser;
  eStatus?: EmsUserLeaveStatus;
  sReason?: string;
  sMessage?: string;
  tCustomIdUser?: string[] | EmsUser[];
  aLeaveCount?: number;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface EmsUserBankDetails {
  _id?: string;
  tIdUser?: string | EmsUser;
  tOrganization?: string | EmsOrganization;
  sBankName?: string;
  sBankAccount?: string;
  sIfscCode?: string;
  sBranchName?: string;
  createdAt?: Date;
  updatedAt?: Date;
  bIsActive?: boolean;
}

export interface EmsUserRequest {
  _id?: string;
  createdAt?: Date;
  updatedAt?: Date;
  tSender: EmsUser | string;
  dStartDate: Date;
  dEndDate: Date;
  tType: EmsManageUserRequest | string;
  tApprovedBy: EmsUser | string;
  eStatus: EmsUserRequestStatus;
  sReason: string;
  sMessage: string;
  sReply?: string;
  tRecipients: EmsUser[] | string[];
  aCount: number;
  bIsHalfDay?: boolean;
  tAttachments?: [
    {
      sFilename?: string;
      sPath?: string;
    }
  ];
  dMultipleDates?: [
    {
      dStartDate?: Date;
      dEndDate?: Date;
    }
  ];
  tOrganization: EmsOrganization | string;
}
export interface EmsSuperAdmin {
  _id?: string;
  createdAt?: Date;
  updatedAt?: Date;
  sName: string;
  sEmail: string;
  sPassword: string;
  aPhoneNumber?: number;
  sProfileUrl?: string;
  bIsActive?: boolean;
  bCanLogin?: boolean;
  bIsDeveloper?: boolean;
}
