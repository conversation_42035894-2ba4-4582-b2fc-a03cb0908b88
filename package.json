{"name": "ems-package", "version": "1.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test", "build-ems": "ng build ems-packages && cd dist/ems-packages && npm pack", "build-azaadi": "ng build azaadi-packages && cd dist/azaadi-packages && npm pack", "build-logistic": "ng build logistic-packages && cd dist/logistic-packages && npm pack", "build-spincabs": "ng build spincabs-packages && cd dist/spincabs-packages && npm pack"}, "private": true, "dependencies": {"@angular/animations": "^14.2.0", "@angular/common": "^14.2.0", "@angular/compiler": "^14.2.0", "@angular/core": "^14.2.0", "@angular/forms": "^14.2.0", "@angular/platform-browser": "^14.2.0", "@angular/platform-browser-dynamic": "^14.2.0", "@angular/router": "^14.2.0", "mongoose": "^7.3.3", "rxjs": "~7.5.0", "tslib": "^2.3.0", "zone.js": "~0.11.4", "moment": "^2.29.4", "moment-timezone": "^0.5.43"}, "devDependencies": {"@angular-devkit/build-angular": "^14.2.10", "@angular/cli": "~14.2.10", "@angular/compiler-cli": "^14.2.0", "@types/jasmine": "~4.0.0", "jasmine-core": "~4.3.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.1.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.0.0", "ng-packagr": "^14.2.0", "typescript": "~4.7.2"}}