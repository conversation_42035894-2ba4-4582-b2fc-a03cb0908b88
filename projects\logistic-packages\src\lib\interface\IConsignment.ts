import { Document, SchemaTimestampsConfig } from "mongoose";
import { IEmployee } from "./IUser";
import { IDriver } from "./IDriver";
import { Status } from "../enums/utilsEnum";

export interface IWareHouse extends Document, SchemaTimestampsConfig {
    sName: string,
    sLocationCode?: string,
    sAddress: string,
    aPinCode: number,
    sCapacity: string,
    tManager: IEmployee | string,
    sDescription?: string,
    bStatus?: boolean,
    bIsOpen?: boolean,
    tCreatedBy?: IEmployee | string,
    sGeoLocation?: string
}
export interface IConsignment extends Document, SchemaTimestampsConfig {
    sDescription?: string,
    sTitle: string,
    sConsignmentNo: string,
    sDeliveryAddress?: string,
    tWarehouse: IWareHouse | string,
    sGeoLocation?: string,
    tDriver?: IDriver | string,
    aQuantity: number,
    aPrice?: number,
    sBillingDetails?: string,
    tItems: IOrderItem[] | string[],
    tCreatedBy?: IEmployee | string,
    eStatus: Status,
    dDeliveryDate?: Date,
}
export interface IInventory extends Document, SchemaTimestampsConfig {
    sItemNo: string,
    sLotNo?: string,
    sLocationCode: string,
    dExpDate?: Date,
    aQuantity: number,
    sDescription?: string,
    eStatus: Status,
    tCreatedBy?: IEmployee | string
}

export interface IOrderItem extends Document, SchemaTimestampsConfig {
    sName?: string,
    sBilltoName?: string,
    sBilltocity?: string,
    sBilltoAddress?: string,
    sGSTNo?: string,
    aUnitPrice?: number,
    aAmount?: number,
    aTotalTax?: number,
    aTCSAmount?: number,
    aContactNo?: string,
    aTotalInvAmount?: number,
    sDocumentNo?: string,
    aLineNo?: number,
    dPostingDate: Date,
    sItemNo?: string,
    aQuantity?: number
    sLocationCode?: string,
}