import { Schema } from 'mongoose';
import { IConcept } from '../Common/common.interface';
import { IUserInfo } from '../User';
import { IOrganization } from './organization.interface';
export interface IRole {
  /** Name of the role */
  sName: string;

  /** Tag/identifier for the role */
  sTag: string;

  /** Reference to the head/parent role */
  tHead: string | Schema.Types.ObjectId | IUserInfo;

  /** Reference to the organization this role belongs to */
  tOrganization: string | Schema.Types.ObjectId | IOrganization;
}
export interface IRoleModel extends IConcept, IRole {}
